/*
 * Copyright (c) 2015-2021 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.service.dao.mgk;

import static com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.generated.Tables.MGK_CM_ARCHIVE;

import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.ArchivePipelineServerCodes;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.ArchivePipelineStatus;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.CmArchiveListConditionBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.CmArchiveListConditionV3Bo;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.generated.tables.records.MgkCmArchiveRecord;
import com.bilibili.sycpb.cpm.scv.app.utils.GrpcUtils;
import com.bilibili.sycpb.cpm.scv.app.utils.NumberUtils;
import com.bilibili.sycpb.cpm.scv.app.utils.constants.ArchiveMode;
import com.bilibili.sycpb.cpm.scv.app.utils.constants.ArchiveState;
import com.bilibili.sycpb.cpm.scv.app.utils.constants.ScvConstants;
import io.jsonwebtoken.lang.Assert;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.SortField;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class MgkCmArchiveDaoService {
    @Resource(name = MgkDataSourceConfig.MGK_DSL_CONTEXT)
    private DSLContext mgk;

    private final RedissonClient redissonClient;


    /**
     * note: 关于素材的注册，统一在triggerCmArchivePipeline 层面完成， 所以不在此处重复处理；
     *
     * @param isWatermarkErase 是否是水印擦除后的视频，注意水印的前置处理记录也置为否，只有处理后的才是true
     */
    @SneakyThrows
    public MgkCmArchiveRecord createIfNotExists(
            Integer archiveMode,
            Integer accountId,
            Long mid,
            String videoUrl,
            String videoMd5,
            String uniqueId,
            Integer source,
            String title,
            Integer biliCategoryOne,
            Integer biliCategoryTwo,
            List<String> tags,
            byte[] ipv6,
            Integer pipelineType,
            Boolean isWatermarkErase
    ) {

        var record = fetchExistingRecord(uniqueId);
        if (Objects.nonNull(record)) {
            return record;
        }

        final var lock = redissonClient.getLock("scv_new_cm_archive_" + uniqueId);
        final var acquired = lock.tryLock(10, TimeUnit.SECONDS);
        if (acquired) {
            try {
                record = fetchExistingRecord(uniqueId);
                if (Objects.nonNull(record)) {
                    return record;
                }

                final var newRecord = mgk.newRecord(MGK_CM_ARCHIVE)
                        .setArchiveMode(archiveMode)
                        .setMid(mid)
                        .setVideoUrl(videoUrl)
                        .setUniqueId(uniqueId)
                        .setAuditStatus(ArchivePipelineServerCodes.PROCESSING.getCode())
                        .setIsWatermarkErase(Optional.ofNullable(isWatermarkErase).map(b -> b ? 1 : 0).orElse(0));
                Optional.ofNullable(accountId)
                        .ifPresent(newRecord::setAccountId);
                Optional.ofNullable(videoMd5)
                        .ifPresent(newRecord::setVideoMd5);
                Optional.ofNullable(source)
                        .ifPresent(newRecord::setSource);
                Optional.ofNullable(title)
                        .ifPresent(newRecord::setTitle);
                Optional.ofNullable(biliCategoryOne)
                        .ifPresent(newRecord::setCatOne);
                Optional.ofNullable(biliCategoryTwo)
                        .ifPresent(newRecord::setCatTwo);
                Optional.ofNullable(ipv6)
                        .ifPresent(newRecord::setIpv6);
                Optional.ofNullable(pipelineType)
                        .ifPresent(newRecord::setPipelineType);
                if (!CollectionUtils.isEmpty(tags)) {
                    newRecord.setTags(String.join(",", tags));
                }

                newRecord.store();
                return newRecord;
            } finally {
                lock.unlock();
            }
        }
        throw new RuntimeException("并发创建投稿流程失败(已有其他流程成功): unique_id=" + uniqueId);
    }

    public MgkCmArchiveRecord fetchExistingRecord(String uniqueId) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(uniqueId)) {
            return null;
        }
        return mgk.fetchOne(MGK_CM_ARCHIVE, MGK_CM_ARCHIVE.UNIQUE_ID.eq(uniqueId)
                .and(MGK_CM_ARCHIVE.IS_DELETED.eq(0)));
    }

    public MgkCmArchiveRecord fetchBrandOgvExistingRecord(String uniqueId) {
        return mgk.fetchOne(MGK_CM_ARCHIVE, MGK_CM_ARCHIVE.UNIQUE_ID.eq(uniqueId)
            .and(MGK_CM_ARCHIVE.ARCHIVE_MODE.eq(ArchiveMode.BRAND_OGV))
                .and(MGK_CM_ARCHIVE.IS_DELETED.eq(0)));
    }

    public MgkCmArchiveRecord fetchUserSpaceExistingRecord(String videoMd5) {
        final var records = mgk.fetch(MGK_CM_ARCHIVE, MGK_CM_ARCHIVE.VIDEO_MD5.eq(videoMd5)
                .and(MGK_CM_ARCHIVE.ARCHIVE_MODE.eq(ArchiveMode.USER_SPACE))
                .and(MGK_CM_ARCHIVE.IS_DELETED.eq(0)));
        Assert.isTrue(records.size() <= 1, "md5="+videoMd5+"对应的稿件已存在");
        return records.isEmpty() ? null : records.get(0);
    }

    public MgkCmArchiveRecord fetchCmSpaceExistingRecord(Integer accountId, String videoMd5, Integer pipelineType) {
        return mgk.fetchOne(MGK_CM_ARCHIVE, MGK_CM_ARCHIVE.VIDEO_MD5.eq(videoMd5)
                .and(MGK_CM_ARCHIVE.ACCOUNT_ID.eq(accountId))
                .and(MGK_CM_ARCHIVE.ARCHIVE_MODE.eq(ArchiveMode.CM_SPACE))
                .and(MGK_CM_ARCHIVE.PIPELINE_TYPE.eq(pipelineType))
                .and(MGK_CM_ARCHIVE.IS_DELETED.eq(0))
        );
    }

    public MgkCmArchiveRecord fetchCmArchive(Integer accountId, String md5) {
        final var records = mgk.fetch(MGK_CM_ARCHIVE, MGK_CM_ARCHIVE.VIDEO_MD5.eq(md5)
            .and(MGK_CM_ARCHIVE.ACCOUNT_ID.eq(accountId))
                .and(MGK_CM_ARCHIVE.IS_DELETED.eq(0)));
        if (records.isEmpty()) return null;

        if (records.size() > 1) throw new RuntimeException(MessageFormat.format("account={0}&video_md5={1}存在非法数据, 请联系系统管理员处理", accountId, md5));
        return records.get(0);
    }

    /**
     * 根据mid 和 md5 获取 archive record
     */
    public MgkCmArchiveRecord fetchCmArchive(Long mid, String md5) {
        return mgk.fetchOne(MGK_CM_ARCHIVE, MGK_CM_ARCHIVE.MID.eq(mid)
            .and(MGK_CM_ARCHIVE.VIDEO_MD5.eq(md5))
                .and(MGK_CM_ARCHIVE.IS_DELETED.eq(0)));
    }

    public Set<Long> fetchCmAvids(Collection<Long> avids) {
        return mgk.select(MGK_CM_ARCHIVE.AVID)
            .from(MGK_CM_ARCHIVE)
            .where(MGK_CM_ARCHIVE.AVID.in(avids)
                    .and(MGK_CM_ARCHIVE.IS_DELETED.eq(0)))
            .fetchSet(MGK_CM_ARCHIVE.AVID);
    }

    public MgkCmArchiveRecord fetchByCid(Long cid) {
        return mgk.fetchOne(MGK_CM_ARCHIVE, MGK_CM_ARCHIVE.CID.eq(cid)
                .and(MGK_CM_ARCHIVE.IS_DELETED.eq(0)));
    }

    public List<MgkCmArchiveRecord> queryCmArchiveByAvids(List<Long> avids) {
        if (CollectionUtils.isEmpty(avids)) {
            return Collections.emptyList();
        }

        return mgk.fetch(MGK_CM_ARCHIVE, MGK_CM_ARCHIVE.ARCHIVE_MODE.eq(ArchiveMode.CM_SPACE)
            .and(MGK_CM_ARCHIVE.AVID.in(avids))
                .and(MGK_CM_ARCHIVE.IS_DELETED.eq(0)));
    }

    public MgkCmArchiveRecord fetchByAvid(Long avid) {
        return mgk.fetchOne(MGK_CM_ARCHIVE, MGK_CM_ARCHIVE.AVID.eq(avid)
                .and(MGK_CM_ARCHIVE.IS_DELETED.eq(0)));
    }

    public void updateOnAuditEvent(MgkCmArchiveRecord mgkCmArchiveRecord, Integer auditStatus, String rejectedReason, boolean registerDm) {
        if (ArchiveState.ok(auditStatus)) {
            mgkCmArchiveRecord.setAuditPassTime(Timestamp.from(Instant.now()));
        }
        if (registerDm) {
            mgkCmArchiveRecord.setRegisterDm(1);
        }
        mgkCmArchiveRecord.setAuditStatus(auditStatus)
            .setAuditReason(rejectedReason)
            .store();
    }

    public List<MgkCmArchiveRecord> list(CmArchiveListConditionV3Bo conditionBo) {
        var condition = MGK_CM_ARCHIVE.ARCHIVE_MODE.eq(conditionBo.getArchiveMode());
        if (!CollectionUtils.isEmpty(conditionBo.getUniqueIds())) {
            condition = condition.and(MGK_CM_ARCHIVE.UNIQUE_ID.in(conditionBo.getUniqueIds()));
        }

        if (!CollectionUtils.isEmpty(conditionBo.getAccountIds())) {
            condition = condition.and(MGK_CM_ARCHIVE.ACCOUNT_ID.in(conditionBo.getAccountIds()));
        }
        if (!CollectionUtils.isEmpty(conditionBo.getVideoMd5s())) {
            condition = condition.and(MGK_CM_ARCHIVE.VIDEO_MD5.in(conditionBo.getVideoMd5s()));
        }
        if (StringUtils.hasText(conditionBo.getTitle())) {
            condition = condition.and(MGK_CM_ARCHIVE.TITLE.like("%" + conditionBo.getTitle() + "%"));
        }
        if (!CollectionUtils.isEmpty(conditionBo.getMids())) {
            condition = condition.and(MGK_CM_ARCHIVE.MID.in(conditionBo.getMids()));
        }
        if (!CollectionUtils.isEmpty(conditionBo.getAvids())) {
            condition = condition.and(MGK_CM_ARCHIVE.AVID.in(conditionBo.getAvids()));
        }
        if (Objects.nonNull(conditionBo.getIsAuditPassed())) {
            if (conditionBo.getIsAuditPassed()) {
                condition = condition.and(MGK_CM_ARCHIVE.AUDIT_STATUS.ge(0));
            } else {
                condition = condition.and(MGK_CM_ARCHIVE.AUDIT_STATUS.lt(0));
            }
        }
        if (Objects.nonNull(conditionBo.getIsDeleted())) {
            condition = condition.and(MGK_CM_ARCHIVE.IS_DELETED.eq(NumberUtils.boolean2Integer(conditionBo.getIsDeleted())));
        }
        if (Objects.nonNull(conditionBo.getMinCtime()) && GrpcUtils.isNotDefaultValue(conditionBo.getMinCtime())) {
            condition = condition.and(MGK_CM_ARCHIVE.CTIME.ge(new Timestamp(conditionBo.getMinCtime())));
        }
        if (Objects.nonNull(conditionBo.getMaxCtime()) && GrpcUtils.isNotDefaultValue(conditionBo.getMaxCtime())) {
            condition = condition.and(MGK_CM_ARCHIVE.CTIME.le(new Timestamp(conditionBo.getMaxCtime())));
        }
        if (Objects.nonNull(conditionBo.getMinDuration()) && GrpcUtils.isNotDefaultValue(conditionBo.getMinDuration())) {
            condition = condition.and(MGK_CM_ARCHIVE.DURATION_IN_MS.greaterOrEqual(conditionBo.getMinDuration()*1000));
        }
        if (Objects.nonNull(conditionBo.getMaxDuration()) && GrpcUtils.isNotDefaultValue(conditionBo.getMaxDuration())) {
            condition = condition.and(MGK_CM_ARCHIVE.DURATION_IN_MS.lessOrEqual(conditionBo.getMaxDuration()*1000));
        }
        if (Objects.nonNull(conditionBo.getRatioWidth()) && GrpcUtils.isNotDefaultValue(conditionBo.getRatioWidth())) {
            condition = condition.and(MGK_CM_ARCHIVE.RATIO_WIDTH.eq(conditionBo.getRatioWidth()));
        }
        if (Objects.nonNull(conditionBo.getRatioHeight()) && GrpcUtils.isNotDefaultValue(conditionBo.getRatioHeight())) {
            condition = condition.and(MGK_CM_ARCHIVE.RATIO_HEIGHT.eq(conditionBo.getRatioHeight()));
        }
        return mgk.selectFrom(MGK_CM_ARCHIVE)
            .where(condition)
            .orderBy(getOrderBy(conditionBo.getOrderBy()))
            .fetch();
    }

    public SortField<Timestamp> getOrderBy(String orderBy) {
        if (!StringUtils.hasText(orderBy)) {
            return MGK_CM_ARCHIVE.MTIME.desc();
        }
        if (orderBy.contains("desc")) {
            if (orderBy.contains("ctime") || orderBy.contains("pub")) {
                return MGK_CM_ARCHIVE.CTIME.desc();
            } else {
                return MGK_CM_ARCHIVE.MTIME.desc();
            }
        }

        if (orderBy.contains("asc")) {
            if (orderBy.contains("ctime") || orderBy.contains("pub")) {
                return MGK_CM_ARCHIVE.CTIME.asc();
            } else {
                return MGK_CM_ARCHIVE.MTIME.asc();
            }
        }
        return MGK_CM_ARCHIVE.MTIME.desc();
    }


    public List<MgkCmArchiveRecord> findByIdGtAndLimit(@Nullable Integer id, int limit,

                                                       LocalDateTime startTime, Integer accountId

    ){
        Condition condition = MGK_CM_ARCHIVE.IS_DELETED.eq(0);

        if (Objects.nonNull(id)) {
            condition = condition.and(MGK_CM_ARCHIVE.ID.greaterThan(id));
        }

        if(Objects.nonNull(startTime)){

            condition = condition.and(MGK_CM_ARCHIVE.CTIME.ge(new Timestamp(
                startTime.toInstant(ZoneOffset.of("+8")).toEpochMilli()
            )));
        }

        if(Objects.nonNull(accountId)){
            condition = condition.and(MGK_CM_ARCHIVE.ACCOUNT_ID.eq(accountId));
        }


        return mgk.selectFrom(MGK_CM_ARCHIVE)
            .where(condition)
            .orderBy(MGK_CM_ARCHIVE.ID.asc())
            .limit(limit)
            .fetch();

    }

    public List<MgkCmArchiveRecord> list(CmArchiveListConditionBo conditionBo) {
        Condition condition = MGK_CM_ARCHIVE.ARCHIVE_MODE.eq(conditionBo.getArchiveMode());
        if (Objects.nonNull(conditionBo.getAccountId())) {
            condition = condition.and(MGK_CM_ARCHIVE.ACCOUNT_ID.eq(conditionBo.getAccountId()));
        }
        if (Objects.nonNull(conditionBo.getMd5s())) {
            condition = condition.and(MGK_CM_ARCHIVE.VIDEO_MD5.in(conditionBo.getMd5s()));
        }
        if (StringUtils.hasText(conditionBo.getTitle())) {
            condition = condition.and(MGK_CM_ARCHIVE.TITLE.like("%" + conditionBo.getTitle() + "%"));
        }
        if (Objects.nonNull(conditionBo.getIsDeleted())) {
            condition = condition.and(MGK_CM_ARCHIVE.IS_DELETED.eq(conditionBo.getIsDeleted()));
        }
        if (Objects.nonNull(conditionBo.getCtimeFrom())) {
            condition = condition.and(MGK_CM_ARCHIVE.CTIME.greaterOrEqual(conditionBo.getCtimeFrom()));
        }
        if (Objects.nonNull(conditionBo.getCtimeTo())) {
            condition = condition.and(MGK_CM_ARCHIVE.CTIME.lessOrEqual(conditionBo.getCtimeTo()));
        }

        if (NumberUtils.isPositive(conditionBo.getMid())) {
            condition = condition.and(MGK_CM_ARCHIVE.MID.eq(conditionBo.getMid()));
        }
        if (Objects.nonNull(conditionBo.getAuditPass())) {
            if (conditionBo.getAuditPass()) {
                condition = condition.and(MGK_CM_ARCHIVE.AUDIT_STATUS.greaterOrEqual(0));
            } else {
                condition = condition.and(MGK_CM_ARCHIVE.AUDIT_STATUS.lt(0));
            }
        }
        if (!CollectionUtils.isEmpty(conditionBo.getAvids())) {
            condition = condition.and(MGK_CM_ARCHIVE.AVID.in(conditionBo.getAvids()));
        }
        // 最小最大时长
        if (NumberUtils.isNonNegative(conditionBo.getMinDuration())) {
            condition = condition.and(MGK_CM_ARCHIVE.DURATION_IN_MS.greaterOrEqual(conditionBo.getMinDuration()*1000));
        }
        if (NumberUtils.isPositive(conditionBo.getMaxDuration())) {
            condition = condition.and(MGK_CM_ARCHIVE.DURATION_IN_MS.lessOrEqual(conditionBo.getMaxDuration()*1000));
        }
        // 只能指定的几个尺寸 and ((width=16 and height=9) or (width=9 and height=16) or (width=3 and height=4) or (width=4 and height=3))
        if (NumberUtils.isPositive(conditionBo.getPointedSize())) {
            condition = condition.and((MGK_CM_ARCHIVE.RATIO_WIDTH.eq(ScvConstants.SIZE_16).and(MGK_CM_ARCHIVE.RATIO_HEIGHT.eq(ScvConstants.SIZE_9)))
                .or((MGK_CM_ARCHIVE.RATIO_WIDTH.eq(ScvConstants.SIZE_9).and(MGK_CM_ARCHIVE.RATIO_HEIGHT.eq(ScvConstants.SIZE_16))))
                .or((MGK_CM_ARCHIVE.RATIO_WIDTH.eq(ScvConstants.SIZE_3).and(MGK_CM_ARCHIVE.RATIO_HEIGHT.eq(ScvConstants.SIZE_4))))
                .or((MGK_CM_ARCHIVE.RATIO_WIDTH.eq(ScvConstants.SIZE_4).and(MGK_CM_ARCHIVE.RATIO_HEIGHT.eq(ScvConstants.SIZE_3)))));
        }
        // 宽高过滤
        if (NumberUtils.isPositive(conditionBo.getRatioWidth())) {
            condition = condition.and(MGK_CM_ARCHIVE.RATIO_WIDTH.eq(conditionBo.getRatioWidth()));
        }
        if (NumberUtils.isPositive(conditionBo.getRatioHeight())) {
            condition = condition.and(MGK_CM_ARCHIVE.RATIO_HEIGHT.eq(conditionBo.getRatioHeight()));
        }
        // 尺寸类型
        if (Objects.nonNull(conditionBo.getSizeType())) {
            condition = condition.and(MGK_CM_ARCHIVE.SIZE_TYPE.eq(conditionBo.getSizeType()));
        }
        return mgk.selectFrom(MGK_CM_ARCHIVE)
            .where(condition)
            .orderBy(MGK_CM_ARCHIVE.MTIME.desc())
            .fetch();
    }

    public List<MgkCmArchiveRecord> listByAvidOrMd5(List<Long> aids , List<String> md5s) {
        //都为空则不查
        if (CollectionUtils.isEmpty(aids) && CollectionUtils.isEmpty(md5s)){
            return new ArrayList<>();
        }

        Condition condition = MGK_CM_ARCHIVE.IS_DELETED.eq(0);
        if (!CollectionUtils.isEmpty(md5s)) {
            condition = condition.and(MGK_CM_ARCHIVE.VIDEO_MD5.in(md5s));
        }

        if (!CollectionUtils.isEmpty(aids)) {
            condition = condition.and(MGK_CM_ARCHIVE.AVID.in(aids));
        }
        return mgk.selectFrom(MGK_CM_ARCHIVE)
            .where(condition)
            .orderBy(MGK_CM_ARCHIVE.MTIME.desc())
            .fetch();
    }

    public List<MgkCmArchiveRecord> fetchCmArchiveByCids(List<Long> cids) {
        if (CollectionUtils.isEmpty(cids)) {
            return Collections.emptyList();
        }

        return mgk.fetch(MGK_CM_ARCHIVE, MGK_CM_ARCHIVE.ARCHIVE_MODE.eq(ArchiveMode.CM_SPACE)
                .and(MGK_CM_ARCHIVE.IS_DELETED.eq(0))
                .and(MGK_CM_ARCHIVE.CID.in(cids))
                .and(MGK_CM_ARCHIVE.PIPELINE_STATUS.eq(ArchivePipelineStatus.FAILED).or(MGK_CM_ARCHIVE.PIPELINE_STATUS.eq(ArchivePipelineStatus.SUCCEEDED)))
        );
    }
    public List<MgkCmArchiveRecord> fetchOldCmArchiveByCids(List<Long> cids) {
        if (CollectionUtils.isEmpty(cids)) {
            return Collections.emptyList();
        }

        return mgk.fetch(MGK_CM_ARCHIVE, MGK_CM_ARCHIVE.ARCHIVE_MODE.eq(ArchiveMode.CM_SPACE)
                .and(MGK_CM_ARCHIVE.IS_DELETED.eq(0))
                .and(MGK_CM_ARCHIVE.CID.in(cids))
                .and(MGK_CM_ARCHIVE.PIPELINE_STATUS.eq(0).and(MGK_CM_ARCHIVE.PIPELINE_STAGE.eq(0).and(MGK_CM_ARCHIVE.UNIQUE_ID.eq(""))))
        );
    }


    //根据cid软删archive
    public void delArchivesByCids(List<Long> cid, Integer accountId) {
        mgk.update(MGK_CM_ARCHIVE)
                .set(MGK_CM_ARCHIVE.IS_DELETED, 1)
                .where(MGK_CM_ARCHIVE.CID.in(cid))
                .and(MGK_CM_ARCHIVE.ACCOUNT_ID.eq(accountId))
                .execute();
    }

    public void delArchivesByAvid(Long accountId, Long aids) {
        mgk.update(MGK_CM_ARCHIVE)
                .set(MGK_CM_ARCHIVE.IS_DELETED, 1)
                .where(MGK_CM_ARCHIVE.ACCOUNT_ID.eq(accountId.intValue()))
                .and(MGK_CM_ARCHIVE.AVID.eq(aids))
                .execute();
    }

}
