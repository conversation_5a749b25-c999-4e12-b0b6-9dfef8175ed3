/*
 * Copyright (c) 2015-2023 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.service.xxl.tempus_jobs;

import com.alibaba.fastjson.JSON;
import com.bapis.ad.adp.component.BusinessToolTypeEnum;
import com.bapis.ad.component.BizCodeEnum;
import com.bapis.ad.component.CommentComponentStatus;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.AdDataSourceConfig;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.tables.TLauArchiveCommentConversionComponent;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.tables.records.LauArchiveCommentConversionComponentRecord;
import com.bilibili.sycpb.cpm.scv.app.service.databus.v1.conversion_component.BusinessNoArchiveComponentDeleteDatabusConfig;
import com.bilibili.sycpb.cpm.scv.app.service.databus.v1.conversion_component.bos.BusinessNoArchiveComponentDeleteMsgBo;
import com.bilibili.sycpb.cpm.scv.app.service.rpc.CommunityRpcService;
import com.bilibili.sycpb.cpm.scv.app.utils.BusinessToolComponentUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.Result;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.component.databus.pub.DatabusPub;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.Tables.LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT;

/**
 * 刷稿件评论组件，c端删除的情况，(经营平台的)
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RefreshBusinessToolArchiveCommentTempusJob implements BasicProcessor {

    public static final String ID = "RefreshBusinessToolArchiveCommentTempusJob";

    private final CommunityRpcService communityRpcService;

    private static final Integer BATCH_SIZE = 400;

    @Resource(name = AdDataSourceConfig.AD_DSL_CONTEXT)
    private DSLContext ad;
    @Resource(name = BusinessNoArchiveComponentDeleteDatabusConfig.BUSINESS_NO_ARCHIVE_COMPONENT_DELETE_PUB)
    private final DatabusPub businessNoArchiveComponentDeletePub;

    @Override
    public ProcessResult process(TaskContext context) throws Exception {
        OmsLogger logger = context.getOmsLogger();
        String param = Optional.ofNullable(context.getJobParams()).orElse("");

        logger.info("RefreshBusinessToolArchiveCommentTempusJob 开始执行, 参数: {}", param);
        log.info("RefreshBusinessToolArchiveCommentTempusJob 开始执行, 参数: {}", param);

        try {
            // 获取最大 id
            Long maxId = fetchMaxId();
            Long index = 0L;
            int totalProcessed = 0;
            int batchCount = 0;

            logger.info("获取最大ID: {}", maxId);

            while (true) {
                Thread.sleep(10);
                if (index * BATCH_SIZE > maxId) {
                    logger.info("经营号处理结束, index={}, maxId={}", index, maxId);
                    log.info("经营号处理结束, index={}, maxId={}", index, maxId);
                    break;
                }

                // 获取一批
                final var oneBatchRecords = fetchBusinessToolArchiveRecordsOpt(param, index);
                batchCount++;
                logger.info("获取第 {} 批经营号组件, size={}, index={}, maxId={}", batchCount, oneBatchRecords.size(), index, maxId);
                log.info("获取第 {} 批经营号组件, size={}, index={}, maxId={}", batchCount, oneBatchRecords.size(), index, maxId);

                if (oneBatchRecords.isEmpty()) {
                    index++;
                    continue;
                }

                for (LauArchiveCommentConversionComponentRecord record : oneBatchRecords) {
                    try {
                        // qps控制在10以下防止触发主站的频控
                        TimeUnit.MILLISECONDS.sleep(100);
                        final var commentDisabled = communityRpcService.isCommentDisabled(record.getAid());
                        if (commentDisabled) {
                            record.setStatus(CommentComponentStatus.COMMENT_AREA_NOT_AVAILABLE_VALUE)
                                    .store();
                            logger.info("评论区已关闭 - avid={}, item_id={}, comment_id={}",
                                       record.getAid(), record.getProductId(), record.getCommentId());
                            log.info("{}: avid={}, item_id={},comment_id={}对应的评论区已关闭", ID, record.getAid(), record.getProductId(), record.getCommentId());
                            totalProcessed++;
                            continue;
                        }
                        final var commentId = communityRpcService.fetchTopCommentId(record.getAid());
                        logger.info("处理c端数据 - avid={}, auditStatus={}, commentId={}, c端稿件评论id={}",
                                   record.getAid(), record.getAuditStatus(), record.getCommentId(), commentId);
                        log.info("{}, job process c端 data, avid={},auditStatus={},commentId={},c端稿件评论id={}", ID, record.getAid(),record.getAuditStatus(),record.getCommentId(), commentId);

                        // 有评论 id，但是c端没有评论
                        if (Objects.equals(commentId, 0L) && !Objects.equals(record.getCommentId(), 0)) {
                            record.setStatus(CommentComponentStatus.DELETED_VALUE)
                                    .setReason("c端用户从移动端删除了评论")
                                    .store();
                            logger.info("置顶评论已删除 - avid={}, item_id={}, comment_id={}",
                                       record.getAid(), record.getProductId(), record.getCommentId());
                            log.info("{}: avid={}, item_id={},comment_id={}对应的评论区置顶评论已删除", ID, record.getAid(), record.getProductId(), record.getCommentId());

                            // 经营平台删除，需要通知经营平台
                            if (Objects.equals(record.getBizCode(), BizCodeEnum.BUSINESS_TOOL_VALUE)) {
                                String relatedOutId = BusinessToolComponentUtils.getRelatedOutId(record);
                                BusinessToolTypeEnum businessToolTypeEnum = BusinessToolComponentUtils.getBusinessToolTypeByClueType(record.getClueType());

                                BusinessNoArchiveComponentDeleteMsgBo databusMsgBo = BusinessNoArchiveComponentDeleteMsgBo.builder()
                                        .avid(record.getAid())
                                        .id(record.getId())
                                        .businessToolId(Long.parseLong(relatedOutId))
                                        .businessToolType(businessToolTypeEnum.getNumber())
                                        .deleteTimestamp(System.currentTimeMillis())
                                        .build();
                                logger.info("发送经营工具删除消息 - aid={}, 消息={}", record.getAid(), JSON.toJSONString(databusMsgBo));
                                log.info("{}, job delete business tool avid component, aid={}, 发送消息={}", ID, record.getAid(), JSON.toJSONString(databusMsgBo));
                                businessNoArchiveComponentDeletePub.pub(JSON.toJSONString(databusMsgBo));
                            }
                            totalProcessed++;
                        } else if (!Objects.equals(commentId, record.getCommentId())) {
                            record.setStatus(CommentComponentStatus.UN_TOP_VALUE)
                                    .store();
                            logger.info("置顶评论已变更 - avid={}, item_id={}, comment_id={} -> {}",
                                       record.getAid(), record.getProductId(), record.getCommentId(), commentId);
                            log.info("{}: avid={}, item_id={},comment_id={}对应的评论区置顶评论已变更 -> {}", ID, record.getAid(), record.getProductId(), record.getCommentId(), commentId);
                            totalProcessed++;
                        }
                    } catch (Exception e) {
                        logger.warn("经营号处理失败 - avid={}, 错误: {}", record.getAid(), e.getMessage());
                        log.error("{}: 经营号处理失败", ID, e);
                    }
                }
                index++;

                if (batchCount % 10 == 0) {
                    log.info("已处理 {} 批, 累计处理: {} 条记录", batchCount, totalProcessed);
                }
            }

            logger.info("RefreshBusinessToolArchiveCommentTempusJob 执行成功, 总共处理 {} 批, {} 条记录", batchCount, totalProcessed);
            log.info("RefreshBusinessToolArchiveCommentTempusJob 执行成功, 总共处理 {} 批, {} 条记录", batchCount, totalProcessed);
            return new ProcessResult(true, "success");
        } catch (Exception e) {
            logger.error("RefreshBusinessToolArchiveCommentTempusJob 执行失败", e);
            log.error("RefreshBusinessToolArchiveCommentTempusJob 执行失败", e);
            return new ProcessResult(false, "执行失败: " + e.getMessage());
        }
    }

    private Long fetchMaxId() {
        Result<LauArchiveCommentConversionComponentRecord> result = ad.selectFrom(LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT)
                .where(LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.STATUS.eq(CommentComponentStatus.TOP_VALUE)
                        .and(LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.BIZ_CODE.eq(BizCodeEnum.BUSINESS_TOOL_VALUE))
                        .and(LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.STATUS.notEqual(CommentComponentStatus.DELETED_VALUE)))
                .orderBy(LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.ID.desc())
                .limit(1)
                .fetch();

        if (CollectionUtils.isEmpty(result)) {
            return 0L;
        }

        return result.get(0).getId();
    }

    private List<LauArchiveCommentConversionComponentRecord> fetchBusinessToolArchiveRecordsOpt(String avidsStr, Long index) {
        // 经营号置顶的稿件
        return ad.selectFrom(LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT)
                .where(LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.STATUS.eq(CommentComponentStatus.TOP_VALUE)
                        .and(LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.ID.greaterOrEqual(index * BATCH_SIZE))
                        .and(TLauArchiveCommentConversionComponent.LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.ID.lessThan((index + 1) * BATCH_SIZE)
                                .and(LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.BIZ_CODE.eq(BizCodeEnum.BUSINESS_TOOL_VALUE))
                                .and(LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.STATUS.notEqual(CommentComponentStatus.DELETED_VALUE))))
                .fetch();
    }
}
