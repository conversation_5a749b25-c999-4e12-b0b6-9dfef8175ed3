/*
 * Copyright (c) 2015-2022 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.service.xxl.tempus_jobs;

import com.alibaba.fastjson.JSON;
import com.bapis.ad.audit.*;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.CommentComponentService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.LaunchArchiveService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.LaunchCreativeService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.bos.CreativeIdsBo;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.tables.records.LauArchiveRecord;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad_core.AdCoreDataSourceConfig;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad_core.generated.tables.records.LauUnitCreativeRecord;
import com.bilibili.sycpb.cpm.scv.app.service.databus.v1.es.LauArchiveChangeLogService;
import com.bilibili.sycpb.cpm.scv.app.service.databus.v1.native_ad.NativeCreativeRelativityRejectBo;
import com.bilibili.sycpb.cpm.scv.app.service.databus.v1.native_ad.NativeCreativeRelativityRejectDatabusConfig;
import com.bilibili.sycpb.cpm.scv.app.service.es.LauArchiveChangeLogRepo;
import com.bilibili.sycpb.cpm.scv.app.service.es.QueryParams;
import com.bilibili.sycpb.cpm.scv.app.service.es.RestEsService;
import com.bilibili.sycpb.cpm.scv.app.service.es.bos.LauArchiveChangeLog;
import com.bilibili.sycpb.cpm.scv.app.service.lancer.LancerReportService;
import com.bilibili.sycpb.cpm.scv.app.service.lancer.param.PersonFlyExt;
import com.bilibili.sycpb.cpm.scv.app.service.rpc.AdAuditService;
import com.bilibili.sycpb.cpm.scv.app.service.rpc.ArchiveRpcService;
import com.bilibili.sycpb.cpm.scv.app.service.wechat.WechatConfig;
import com.bilibili.sycpb.cpm.scv.app.utils.NumberUtils;
import com.bilibili.sycpb.cpm.scv.app.utils.metrics.CustomMetrics;
import com.bilibili.sycpb.cpm.scv.app.utils.metrics.MetricsKeyConstant;
import com.google.common.collect.Lists;
import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.common.Attributes;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.unit.Fuzziness;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import pleiades.component.databus.pub.DatabusPub;
import pleiades.venus.starter.rpc.client.RPCClient;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bilibili.sycpb.cpm.scv.app.service.dao.ad_core.generated.Tables.LAU_PROGRAMMATIC_CREATIVE_DETAIL;
import static com.bilibili.sycpb.cpm.scv.app.service.dao.ad_core.generated.Tables.LAU_UNIT_CREATIVE;
import static com.bilibili.sycpb.cpm.scv.app.utils.metrics.MetricsKeyConstant.count_type_handleArchiveChangeLog;
import static com.bilibili.sycpb.cpm.scv.app.utils.metrics.MetricsKeyConstant.handleArchiveChangeLogType;

/**
 * 刷新稿件状态任务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RefreshArchiveStateTempusJob implements BasicProcessor {

    @Value("${alarm.RefreshArchiveStateJob.delay:1800000}")
    private long delay;

    public static final String ID = "RefreshArchiveStateTempusJob";

    public static final Integer ARCHIVE_UPDATE_CID_CHANGE = 1;
    public static final Integer ARCHIVE_STAT_UPDATE = 2;

    private final RestEsService restEsService;
    private final LaunchArchiveService launchArchiveService;
    private final ArchiveRpcService archiveRpcService;
    private final AdAuditService adAuditService;
    private final CommentComponentService commentComponentService;
    private final LaunchCreativeService launchCreativeService;

    private final LauArchiveChangeLogService lauArchiveChangeLogService;
    private final LancerReportService lancerReportService;

    @RPCClient("sycpb.cpm.cpm-adp")
    private AuditServiceGrpc.AuditServiceBlockingStub auditServiceBlockingStub;

    @Resource
    private CustomMetrics customMetrics;

    @Resource(name = AdCoreDataSourceConfig.AD_CORE_DSL_CONTEXT)
    private DSLContext adCore;
    @Resource(name = NativeCreativeRelativityRejectDatabusConfig.NATIVE_CREATIVE_RELATIVITY_REJECT_PUB)
    private final DatabusPub nativeCreativeRelativityRejectPub;

    // 原生稿件驳回的 type
    private static final int NATIVE_RELATIVITY_DATABUS_ARCHIVE_REJECT = 3;
    // 原生稿件驳回并生成待审任务 type
    private static final int NATIVE_ARCHIVE_AUDIT_REJECT_GEN_TO_AUDIT = 4;
    @Autowired
    private WechatConfig.Property property;

    @Resource
    private LauArchiveChangeLogRepo lauArchiveChangeLogRepo;

    @Override
    public ProcessResult process(TaskContext context) throws Exception {
        OmsLogger logger = context.getOmsLogger();
        String param = Optional.ofNullable(context.getJobParams()).orElse("");

        logger.info("RefreshArchiveStateTempusJob 开始执行, 参数: {}", param);
        log.info("RefreshArchiveStateTempusJob 开始执行, 参数: {}", param);

        try {
            final int batchSize;
            if (StringUtils.hasText(param)) {
                batchSize = Integer.parseInt(param);
            } else {
                batchSize = 5000;
            }
            logger.info("使用批次大小: {}", batchSize);

            // 处理稿件变化
            handleArchiveChangeLog(batchSize, logger);

            logger.info("RefreshArchiveStateTempusJob 执行成功");
            log.info("RefreshArchiveStateTempusJob 执行成功");
            return new ProcessResult(true, "success");
        } catch (Throwable t) {
            logger.error("RefreshArchiveStateTempusJob 执行失败", t);
            log.error(ID, t);
            return new ProcessResult(false, "执行失败: " + t.getMessage());
        }
    }

    private void handleArchiveChangeLog(int batchSize, OmsLogger logger) {
        logger.info("开始处理稿件变化日志, 批次大小: {}", batchSize);
        processHandleArchiveChangeLog(batchSize, logger);
    }

    private QueryBuilder buildSearchQuery(QueryParams queryParams) {
        if (queryParams.isMatchAll()) return QueryBuilders.matchAllQuery();

        final BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        queryParams.getRanges().forEach((k, v) -> {
            final RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery(k);
            rangeQueryBuilder.from(v._1, true);
            rangeQueryBuilder.to(v._2, false);
            boolQueryBuilder.filter(rangeQueryBuilder);
        });
        queryParams.getTerms().forEach((k, v) -> {
            boolQueryBuilder.filter(QueryBuilders.termsQuery(k, v));
        });
        queryParams.getMatches().forEach((k, v) -> {
            boolQueryBuilder.must(QueryBuilders.matchQuery(k, v).fuzziness(Fuzziness.AUTO));
        });
        return boolQueryBuilder;
    }

    private boolean processHandleArchiveChangeLog(int batchSize, OmsLogger logger) {
        final var t0 = Instant.now();

        // process_ts=0，未待处理，处理过的会更新 process_ts
        final var params = QueryParams.newInstance()
                .addTerm("process_ts", 0)
                .setSize(batchSize);

        final var searchSourceBuilder = new SearchSourceBuilder().query(buildSearchQuery(params));
        searchSourceBuilder.from(params.getPage());
        searchSourceBuilder.size(params.getSize());
        final var changeLogs = lauArchiveChangeLogRepo.search(searchSourceBuilder);

        if (CollectionUtils.isEmpty(changeLogs)) {
            logger.info("处理数据0条, 耗时{}", Duration.between(t0, Instant.now()));
            log.info("{}: 处理数据0条, 耗时{}", ID, Duration.between(t0, Instant.now()));
            return false;
        }

        final var avids = changeLogs.stream()
                .map(LauArchiveChangeLog::getAvid)
                .distinct()
                .collect(Collectors.toList());
        final var lauArchiveRecords = launchArchiveService.listLauArchives(avids);
        if (CollectionUtils.isEmpty(lauArchiveRecords)) {
            batchUpdate(changeLogs, t0.toEpochMilli(), logger);
            logger.info("处理数据{}条, 有效数据0条, 耗时{}", changeLogs.size(), Duration.between(t0, Instant.now()));
            log.info("{}: 处理数据{}条, 有效数据0条, 耗时{}", ID, changeLogs.size(), Duration.between(t0, Instant.now()));
            return batchSize == changeLogs.size();
        }

        final var avid2Record = lauArchiveRecords.stream()
                .collect(Collectors.toMap(LauArchiveRecord::getAid, Function.identity()));
        final var affectedChangeLogs = changeLogs.stream()
                .filter(x -> avid2Record.containsKey(x.getAvid()))
                .collect(Collectors.toList());

        // 对驳回的稿件做创意下线操作
        final List<Long> rejectedAvids = new LinkedList<>();
        // 对新增的稿件做换源检查
        final List<LauArchiveRecord> possibleCidChangedRecords = new LinkedList<>();
        // 对恢复正常的稿件处理
        final List<Long> acceptAvids = new LinkedList<>();
        for (LauArchiveChangeLog changeLog : affectedChangeLogs) {
            // cid 可能变更的，可能换源
            if (LauArchiveChangeLog.possibleCidChange(changeLog.getPrevArchiveState(), changeLog.getArchiveState())) {
                possibleCidChangedRecords.add(avid2Record.get(changeLog.getAvid()));
            }
            // 稿件状态变更的: 状态由开放浏览(或定时发布)变为非开放浏览
            else if (LauArchiveChangeLog.assuredRejection(changeLog.getPrevArchiveState(), changeLog.getArchiveState())) {
                rejectedAvids.add(changeLog.getAvid());
            }
            // 稿件状态变更 状态由非开发浏览变为开放浏览
            else if (LauArchiveChangeLog.assuredAccept(changeLog.getPrevArchiveState(), changeLog.getArchiveState())) {
                acceptAvids.add(changeLog.getAvid());
            }
        }
        int cidChangedAvidsLen = 0;

        if (!CollectionUtils.isEmpty(possibleCidChangedRecords)) {
            // 处理可能的换源问题
            final var cidChangedAvids = handleCidChangedArchives(possibleCidChangedRecords, logger);
            if (!CollectionUtils.isEmpty(cidChangedAvids)) {
                cidChangedAvidsLen = cidChangedAvids.size();
                // 稿件换源，拒审处理
                logger.info("稿件换源 -> avids={}", cidChangedAvids);
                log.info("{}: 稿件换源 -> avids={}", ID, cidChangedAvids);
                handleRejection(cidChangedAvids, ARCHIVE_UPDATE_CID_CHANGE, "稿件内容变更, 需要重新审核", "稿件内容变更", logger);
            }
        }
        if (!CollectionUtils.isEmpty(rejectedAvids)) {
            // 稿件驳回，拒审处理
            logger.info("稿件驳回 -> avids={}", rejectedAvids);
            log.info("{}: 稿件驳回 -> avids={}", ID, rejectedAvids);
            handleRejection(rejectedAvids, ARCHIVE_STAT_UPDATE, "绑定的稿件状态变为不可浏览", "稿件被驳回", logger);
            handleRejectCommentComponent(rejectedAvids);
        }
        if (!CollectionUtils.isEmpty(acceptAvids)) {
            handleAcceptCommentComponent(acceptAvids);
        }

        // 更新 es: lau_archive_change_log 时间戳为 now 的时间戳，标记处理过了
        batchUpdate(changeLogs, t0.toEpochMilli(), logger);

        logger.info("处理数据{}条,  有效数据{}条, 驳回稿件{}个, 拒审稿件换源{}个, 耗时{}"
                , changeLogs.size(), affectedChangeLogs.size(), rejectedAvids.size(), cidChangedAvidsLen, Duration.between(t0, Instant.now()));
        log.info("{}: 处理数据{}条,  有效数据{}条, 驳回稿件{}个, 拒审稿件换源{}个, 耗时{}"
                , ID, changeLogs.size(), affectedChangeLogs.size(), rejectedAvids.size(), cidChangedAvidsLen, Duration.between(t0, Instant.now()));
        try {
            long minTime = changeLogs.stream().filter(t -> t.getEventTs() != null).min(Comparator.comparingInt(t -> t.getEventTs().intValue())).get().getEventTs();
            if (Timestamp.valueOf(LocalDateTime.now()).getTime() - minTime > delay) {
                customMetrics.count(10000, Attributes.of(AttributeKey.stringKey("count_type"), "refresh_archive_state_job_delay"));
            }
        } catch (Exception ignored) {
        }
        return changeLogs.size() == batchSize;
    }

    @SneakyThrows
    public void handleRejection(List<Long> avids, Integer archiveUpdateType, String creativeRejectReason, String materialRejectReason, OmsLogger logger) {
        if (!CollectionUtils.isEmpty(avids)) {
            CreativeIdsBo rejectingCreativeIdsBo = launchArchiveService.fetchCreativeIds(avids, false);
            CreativeIdsBo rejectingProgCreativeIdsBo = launchArchiveService.fetchCreativeIds(avids, true);
            // 非程序化创意
            final var rejectingCreativeIdSet = rejectingCreativeIdsBo.getCreativeIds();
            // 主表程序化创意
            final var rejectingProgCreativeIdSet = rejectingProgCreativeIdsBo.getCreativeIds();
            // 附表程序化创意
            final var resultBo = launchArchiveService.fetchProgCreativeIds(avids);
            rejectingProgCreativeIdSet.addAll(resultBo.getRejectingCreativeIds());
            rejectingCreativeIdSet.addAll(rejectingProgCreativeIdSet);
            if (!CollectionUtils.isEmpty(rejectingProgCreativeIdSet)) {
                // 变更其他元素审核状态
                logger.info("修改其他元素审核状态, avids={}, creative_ids={}", avids, rejectingProgCreativeIdSet);
                log.info("修改其他元素审核状态, avids={}, creative_ids={}", avids, rejectingProgCreativeIdSet);
                adCore.update(LAU_UNIT_CREATIVE)
                        .set(LAU_UNIT_CREATIVE.PROG_MISC_ELEM_AUDIT_STATUS, 2)
                        .where(LAU_UNIT_CREATIVE.CREATIVE_ID.in(rejectingProgCreativeIdSet)
                                .and(LAU_UNIT_CREATIVE.PROG_MISC_ELEM_AUDIT_STATUS.eq(0)))
                        .execute();
            }
            if (!CollectionUtils.isEmpty(resultBo.getProgDetailIds())) {
                // 拒绝相应元素
                logger.info("修改主元素审核状态, avids={}, creative_ids={}", avids, rejectingProgCreativeIdSet);
                log.info("修改主元素审核状态, avids={}, creative_ids={}", avids, rejectingProgCreativeIdSet);
                adCore.update(LAU_PROGRAMMATIC_CREATIVE_DETAIL)
                        .set(LAU_PROGRAMMATIC_CREATIVE_DETAIL.BIZ_STATUS, 2)
                        .set(LAU_PROGRAMMATIC_CREATIVE_DETAIL.REJECTED_REASON, materialRejectReason)
                        .where(LAU_PROGRAMMATIC_CREATIVE_DETAIL.ID.in(resultBo.getProgDetailIds()))
                        .execute();
            }

            List<LauUnitCreativeRecord> lauUnitCreativeRecords = launchCreativeService.fetchByCreativeIds(new ArrayList<>(rejectingCreativeIdSet));
            Map<Integer, LauUnitCreativeRecord> creativeRecordMap = lauUnitCreativeRecords.stream().collect(Collectors.toMap(t -> t.getCreativeId(), t -> t));

            logger.info("拒审稿件, rejectingCreativeIdSet.size={}, rejectingProgCreativeIdSet.size={}", rejectingCreativeIdSet.size(), rejectingProgCreativeIdSet.size());
            log.info("{}: 拒审稿件, rejectingCreativeIdSet.size={}, rejectingProgCreativeIdSet.size={}", ID, rejectingCreativeIdSet.size(), rejectingProgCreativeIdSet.size());
            for (var rejectingCreativeId : rejectingCreativeIdSet) {

                LauUnitCreativeRecord lauUnitCreativeRecord = creativeRecordMap.get(rejectingCreativeId);
                if (Objects.isNull(lauUnitCreativeRecord)) {
                    logger.error("creativeId={}不存在", rejectingCreativeId);
                    log.error("{}: creativeId={}不存在", ID, rejectingCreativeId);
                    continue;
                }
                if (Objects.equals(lauUnitCreativeRecord.getAuditStatus(), AuditStatus.REJECTED_VALUE)) {
                    logger.info("creativeId={}已经被拒审,跳过", rejectingCreativeId);
                    log.info("{}: creativeId={}已经被拒审,跳过", ID, rejectingCreativeId);
                    continue;
                }

                try {
                    String reason = creativeRejectReason;
                    // 个人起飞的驳回理由
                    if (rejectingCreativeIdsBo.getPersonalCreativeIds().contains(rejectingCreativeId) || rejectingProgCreativeIdsBo.getPersonalCreativeIds().contains(rejectingCreativeId)) {
                        if (Objects.equals(ARCHIVE_UPDATE_CID_CHANGE, archiveUpdateType)) {
                            reason = "稿件内容变更，请在新内容审核通过后重新创建订单";
                        }
                        // 稿件状态变更，不处理个人起飞
                        else if (Objects.equals(ARCHIVE_STAT_UPDATE, archiveUpdateType)) {
                            logger.info("个人起飞状态状态变更，跳过, creativeId={},reason={}", rejectingCreativeId, reason);
                            log.info("{}: 个人起飞状态状态变更，跳过, creativeId={},reason={}", ID, rejectingCreativeId, reason);
                            continue;
                        }
                    }

                    adAuditService.auditReject(rejectingCreativeId, reason);
                    lancerReportService.report(ARCHIVE_UPDATE_CID_CHANGE.equals(archiveUpdateType) ? "REJECT_SYS_AUDIT_CHANGE_SOURCE" : "REJECT_SYS_AUDIT_OTHER"
                            , "PERSONAL_FLY", String.valueOf(rejectingCreativeId)
                            , JSON.toJSONString(PersonFlyExt.builder().reject3rdReason(reason).build()));
                    logger.info("creativeId={}拒审成功, reason={}", rejectingCreativeId, reason);
                    log.info("{}: creativeId={}拒审成功, reason={}", ID, rejectingCreativeId, reason);
                } catch (Throwable t) {
                    logger.error("creativeId={}拒审失败", rejectingCreativeId, t);
                    log.error("{}: creativeId={}拒审失败", ID, rejectingCreativeId, t);
                }
            }

            // 原生的稿件审核拒绝 flyUsages
            try {
                auditRejectNativeAd(avids, archiveUpdateType, creativeRejectReason, logger);
            } catch (Exception e) {
                logger.error("auditRejectNativeAd error,avids={},archiveUpdateType={},creativeRejectReason={}", avids, archiveUpdateType, creativeRejectReason, e);
                log.error("auditRejectNativeAd error,avids={},archiveUpdateType={},creativeRejectReason={}", avids, archiveUpdateType, creativeRejectReason, e);
            }
        }
    }

    /**
     * 处理原生审核驳回
     * 包含: 1.状态由开放浏览(或定时发布)变为非开放浏览 2.换源
     *
     * @param avids 包含了个人起飞的
     */
    public void auditRejectNativeAd(List<Long> avids, Integer archiveUpdateType, String creativeRejectReason, OmsLogger logger) {

        if (CollectionUtils.isEmpty(avids)) {
            return;
        }

        QueryNativeArchivesReq queryNativeArchivesReq = QueryNativeArchivesReq.newBuilder()
                .addAllAvids(avids)
                .setNativeBodyType(NativeBodyType.ARCHIVE_VALUE)
                .build();
        QueryNativeArchivesReply queryNativeArchivesReply = auditServiceBlockingStub.queryNativeArchives(queryNativeArchivesReq);
        Map<Long, SingleQueryNativeArchivesReply> nativeArchivesReplyMap = queryNativeArchivesReply.getListList().stream().collect(Collectors.toMap(t -> t.getAvid(), t -> t, (t1, t2) -> t1));

        Integer optType = NATIVE_RELATIVITY_DATABUS_ARCHIVE_REJECT;
        // cid 变更，需要先驳回再生成待审任务
        if (Objects.equals(archiveUpdateType, ARCHIVE_UPDATE_CID_CHANGE)) {
            optType = NATIVE_ARCHIVE_AUDIT_REJECT_GEN_TO_AUDIT;
        }

        for (Long avid : avids) {
            logger.info("auditRejectNativeAd, 处理稿件:{}", avid);
            log.info("auditRejectNativeAd, 处理稿件:{}", avid);
            SingleQueryNativeArchivesReply nativeArchivesReply = nativeArchivesReplyMap.get(avid);
            if (nativeArchivesReply == null) {
                continue;
            }

            // 发送消息，运营后台处理(稿件驳回之类)
            NativeCreativeRelativityRejectBo relativityRejectBo = NativeCreativeRelativityRejectBo.builder()
                    .opt(optType)
                    .avids(Lists.newArrayList(avid))
                    .reason(creativeRejectReason)
                    .build();
            nativeCreativeRelativityRejectPub.pub(JSON.toJSONString(relativityRejectBo));
        }
    }

    private List<Long> handleCidChangedArchives(List<LauArchiveRecord> records, OmsLogger logger) {
        final List<Long> cidChangedAvids = new LinkedList<>();
        for (LauArchiveRecord record : records) {
            final var arcReply = archiveRpcService.arcReply(record.getAid());

            final var avid = record.getAid();
            final var curCid = record.getCid();
            final var newCid = arcReply.getArc().getFirstCid();
            if (!Objects.equals(newCid, curCid)) {
                cidChangedAvids.add(avid);
                record.setCid(newCid)
                        .store();
                logger.info("avid={}对应的cid变更: {} -> {}", avid, curCid, newCid);
                log.info("{}: avid={}对应的cid变更: {} -> {}", ID, avid, curCid, newCid);
            }
        }
        return cidChangedAvids;
    }

    private void batchUpdate(List<LauArchiveChangeLog> changeLogs, long ts, OmsLogger logger) {
        changeLogs.forEach(x -> x.setProcessTs(ts));
        lauArchiveChangeLogService.batchPub(changeLogs, "update");
        logger.info("批量更新变更日志, 数量: {}", changeLogs.size());
    }

    private void handleRejectCommentComponent(List<Long> avids) {
        commentComponentService.updateRejectArchiveComponent(avids);
    }

    private void handleAcceptCommentComponent(List<Long> avids) {
        commentComponentService.updateAcceptArchiveComponent(avids);
    }

    private Param parse(String s) {
        final var splits = s.split("=");
        final var param = new Param();
        if (Objects.equals(splits[0], "startTs")) {
            param.startTs = NumberUtils.parseIntoTimestamp(splits[1]);
        }
        return param;
    }

    @Getter
    @Setter
    private static class Param {
        private Timestamp startTs;
    }
}