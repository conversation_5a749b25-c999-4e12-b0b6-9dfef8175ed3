package com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.event;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/4/28
 */
@Data
public class MaterialVideo {


    /**
     * 素材持有者accountId
     */
    private Integer accountId;


    private List<Integer> shareTargetAccountIds;


    /**
     * 素材id
     */
    private String materialId;

    /**
     * 素材类型， 与 {MaterialIdType.name()} 保持一致
     */
    private String materialType;

    /**
     * 素材唯一键，一般为md5
     */
    private String materialUk;

    /**
     * 素材名称，多次创建时，同素材id多次上传，取最新名称
     */
    private String materialName;


    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date ctime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date mtime;


    /**
     * 审核状态， 目前仅视频素材有
     */
    private String auditStatus;

    /**
     * 宽
     */
    private Integer width;

    /**
     * 高
     */
    private Integer height;

    /**
     * {@link RatioTypeText#toText(int, int)}
     */
    private String ratioTypeText;


    /**
     * 视频专有，时长秒数
     */
    private Integer duration;

    private Long avid;

    private Long cid;

    private String coverUrl;

    private String coverMd5;


    private Boolean isDeleted;

    /**
     * 非过滤字段，可以用这个结构进行冗余存储
     */
    private String extra;

    private String strAttr1;


}
