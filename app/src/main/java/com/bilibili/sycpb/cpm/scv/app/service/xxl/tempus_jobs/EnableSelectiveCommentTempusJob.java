/*
 * Copyright (c) 2015-2022 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.service.xxl.tempus_jobs;

import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.CmArchiveCommentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import java.util.Optional;

/**
 * 启用精选评论任务
 * 子账号投稿默认开启精选评论 因为主站接口有概率会失败, 所以定时查看回刷
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EnableSelectiveCommentTempusJob implements BasicProcessor {
    
    public static final String ID = "EnableSelectiveCommentTempusJob";
    private final CmArchiveCommentService cmArchiveCommentService;

    @Override
    public ProcessResult process(TaskContext context) throws Exception {
        OmsLogger logger = context.getOmsLogger();
        String jobParams = Optional.ofNullable(context.getJobParams()).orElse("");
        
        logger.info("EnableSelectiveCommentTempusJob 开始执行, 参数: {}", jobParams);
        log.info("EnableSelectiveCommentTempusJob 开始执行, 参数: {}", jobParams);
        
        try {
            logger.info("开始启用评论和精选评论功能");
            cmArchiveCommentService.enableCommentAndSelectiveComment();
            
            logger.info("EnableSelectiveCommentTempusJob 执行成功");
            log.info("EnableSelectiveCommentTempusJob 执行成功");
            return new ProcessResult(true, "success");
        } catch (Throwable t) {
            logger.error("EnableSelectiveCommentTempusJob 执行失败", t);
            log.error("EnableSelectiveCommentTempusJob 执行失败", t);
            return new ProcessResult(false, "执行失败: " + t.getMessage());
        }
    }
}
