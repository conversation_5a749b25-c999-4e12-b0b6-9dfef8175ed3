/*
 * Copyright (c) 2015-2023 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm;

import com.bapis.ad.archive.AggregationInfo;
import com.bapis.ad.archive.CmArchiveAuditInfo;
import com.bapis.ad.archive.CmArchiveCoverInfo;
import com.bapis.ad.archive.CmArchiveInfo;
import com.bapis.ad.archive.CmArchiveUgcInfo;
import com.bapis.ad.archive.CmArchiveVideoInfo;
import com.bapis.ad.archive.CreateCmArchiveReq;
import com.bapis.ad.archive.ListCmArchiveReq;
import com.bilibili.sycpb.cpm.scv.app.controller.open.lib.archive.bos.CmArchiveOpenApiPipelineReqBo;
import com.bilibili.sycpb.cpm.scv.app.controller.open.lib.archive.bos.CmArchivePipelineReqBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm.bos.CmArchiveContextBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm.bos.CmArchiveDataBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm.bos.CmArchivePipelineMetaBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm.bos.CmArchivePipelineMsgBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.AuditBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.CmArchiveListConditionV3Bo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.CmArchiveV3Bo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.CoverBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.MaterialAggregationInfoBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.MgkCmArchiveBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.UgcBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.VideoBo;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.generated.tables.records.MgkCmArchiveRecord;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.generated.tables.records.MgkCmVideoRecord;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED)
public interface CmArchiveConverter {
    CmArchiveConverter MAPPER = Mappers.getMapper(CmArchiveConverter.class);


    default CmArchivePipelineReqBo reqBo2Info(CmArchivePipelineReqBo req) {

        return req;
    }


    default CmArchivePipelineReqBo toReqBo(CmArchivePipelineReqBo req) {


        return req;

    }



    @Mapping(target = "disableExternalAccess", ignore = true)
    @Mapping(target = "isMapi", ignore = true)
    @Mapping(target = "videoUrl", ignore = true)
    @Mapping(target = "uniqueId", ignore = true)
    @Mapping(target = "title", ignore = true)
    @Mapping(target = "tags", ignore = true)
    @Mapping(target = "stage", ignore = true)
    @Mapping(target = "source", ignore = true)
    @Mapping(target = "signature", ignore = true)
    @Mapping(target = "record", ignore = true)
    @Mapping(target = "pipeline", ignore = true)
    @Mapping(target = "maxSizeKb", ignore = true)
    @Mapping(target = "localStorageBo", ignore = true)
    @Mapping(target = "ipv6", ignore = true)
    @Mapping(target = "downloadFromMd5", ignore = true)
    @Mapping(target = "coverUrl", ignore = true)
    @Mapping(target = "biliCategoryTwo", ignore = true)
    @Mapping(target = "biliCategoryOne", ignore = true)
    @Mapping(target = "archiveMode", ignore = true)
    CmArchiveContextBo fromWebApiBo(CmArchivePipelineReqBo bo);


    @Mapping(target = "disableExternalAccess", ignore = true)
    @Mapping(target = "isMapi", constant = "true")
    @Mapping(target = "stage", ignore = true)
    @Mapping(target = "ipv6", ignore = true)
    @Mapping(target = "source", ignore = true)
    @Mapping(target = "pipeline", ignore = true)
    @Mapping(target = "maxSizeKb", ignore = true)
    @Mapping(target = "downloadFromMd5", ignore = true)
    @Mapping(target = "videoUrl", ignore = true)
    @Mapping(target = "uniqueId", ignore = true)
    @Mapping(target = "signature", ignore = true)
    @Mapping(target = "localStorageBo", ignore = true)
    @Mapping(target = "record", ignore = true)
    @Mapping(target = "tags", source = "bo.biliVideoTags")
    @Mapping(target = "coverUrl", source = "bo.cover.url")
    @Mapping(target = "biliCategoryTwo", source = "bo.biliCatTwo")
    @Mapping(target = "biliCategoryOne", source = "bo.biliCatOne")
    @Mapping(target = "enableSelectiveComment", source = "bo.enableSelectiveComment")
    CmArchiveContextBo fromOpenApiBo(CmArchiveOpenApiPipelineReqBo bo, int archiveMode);

    @Mapping(target = "isMapi", ignore = true)
    @Mapping(target = "stage", ignore = true)
    @Mapping(target = "ipv6", ignore = true)
    @Mapping(target = "record", ignore = true)
    @Mapping(target = "localStorageBo", ignore = true)
    @Mapping(target = "pipeline", ignore = true)
    @Mapping(target = "maxSizeKb", ignore = true)
    @Mapping(target = "downloadFromMd5", ignore = true)
    @Mapping(target = "signature", ignore = true)
    @Mapping(target = "uniqueId", source = "req.cmArchiveContext.uniqueId")
    @Mapping(target = "tags", source = "req.tagSet")
    @Mapping(target = "source", source = "req.cmArchiveContext.source")
    @Mapping(target = "mid", source = "req.cmArchiveContext.mid")
    @Mapping(target = "accountId", source = "req.cmArchiveContext.accountId")
    @Mapping(target = "disableExternalAccess", source = "req.disableExternalAccess")
    CmArchiveContextBo fromRpcBo(int archiveMode, CreateCmArchiveReq req);

    @Mapping(target = "data", expression = "java(toDataBo(record))")
    @Mapping(target = "meta", expression = "java(toMetaBo(record))")
    CmArchivePipelineMsgBo toMsgBo(int code, String message, MgkCmArchiveRecord record);

    CmArchivePipelineMetaBo toMetaBo(MgkCmArchiveRecord record);

    @Mapping(target = "sizeInKb", source = "sizeKb")
    CmArchiveDataBo toDataBo(MgkCmArchiveRecord record);

    @Mapping(target = "videoMd5s", source = "videoMd5Set")
    @Mapping(target = "mids", source = "midSet")
    @Mapping(target = "isShared", expression = "java(com.bilibili.sycpb.cpm.scv.app.utils.NumberUtils.booSet2Boolean(req.getIsSharedSetList()))")
    @Mapping(target = "isDeleted", expression = "java(com.bilibili.sycpb.cpm.scv.app.utils.NumberUtils.booSet2Boolean(req.getIsDeletedSetList()))")
    @Mapping(target = "isAuditPassed", expression = "java(com.bilibili.sycpb.cpm.scv.app.utils.NumberUtils.booSet2Boolean(req.getIsAuditPassedSetList()))")
    @Mapping(target = "avids", source = "avIdSet")
    @Mapping(target = "accountIds", source = "accountIdSet")
    @Mapping(target = "archiveMode", expression = "java(req.getArchiveModeValue())")
    @Mapping(target = "accountIdsInAggregationView", source = "accountIdsForAggregationView")
    CmArchiveListConditionV3Bo fromRpcBo(ListCmArchiveReq req);

    CmArchiveCoverInfo fromBo(CoverBo bo);
    @Mapping(target = "biliCategoryTwo", source = "catTwo")
    @Mapping(target = "biliCategoryOne", source = "catOne")
    CmArchiveUgcInfo fromBo(UgcBo bo);

    CmArchiveVideoInfo fromBo(VideoBo bo);

    @Mapping(target = "message", source = "value")
    @Mapping(target = "code", source = "key")
    CmArchiveAuditInfo fromBo(AuditBo bo);


    AggregationInfo fromBo(MaterialAggregationInfoBo bo);

    CmArchiveInfo fromBo(CmArchiveV3Bo bo);

    List<CmArchiveInfo> fromBos(List<CmArchiveV3Bo> bos);

    @Mapping(target = "url", source = "coverUrl")
    @Mapping(target = "md5", source = "coverMd5")
    CoverBo toCoverBo(MgkCmArchiveBo record);


    @Mapping(target = "md5", source = "record.videoMd5")
    @Mapping(target = "duration", source = "record.durationInMs")
    @Mapping(target = "size", expression = "java(record.getSizeKb()*1024)")
    @Mapping(target = "ctime", expression = "java(ctime/1000)")
    @Mapping(target = "archiveSource", source = "record.archiveSource")
    VideoBo toVideoBo(MgkCmArchiveBo record, String name, Long ctime);

    @Mapping(target = "tags", expression = "java(java.util.Arrays.stream(record.getTags().split(\",\")).filter(x -> !x.isEmpty()).collect(java.util.stream.Collectors.toList()))")
    UgcBo toUgcBo(MgkCmArchiveBo record);

    @Mapping(target = "value", expression = "java(com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.ArchivePipelineServerCodes.getDesc(record.getAuditStatus()))")
    @Mapping(target = "key", source = "auditStatus")
    @Mapping(target = "reason", source = "auditReason")
    @Mapping(target = "pubTime", expression = "java(record.getAuditPassTime().getTime()/1000)")
    AuditBo toAuditBo(MgkCmArchiveBo record);

    @Mapping(target = "cover", expression = "java(toCoverBo(archiveRecord))")
    @Mapping(target = "video", expression = "java(toVideoBo(archiveRecord, java.util.Optional.ofNullable(videoRecord).map(MgkCmVideoRecord::getRawName).orElse(\"\"), java.util.Optional.ofNullable(videoRecord).map(x -> x.getCtime().getTime()).orElse(0L)))")
    @Mapping(target = "ugc", expression = "java(toUgcBo(archiveRecord))")
    @Mapping(target = "audit", expression = "java(toAuditBo(archiveRecord))")
    @Mapping(target = "aggregation", expression = "java(toAggregationBo(archiveRecord))")
    CmArchiveV3Bo toCmArchiveBo(MgkCmArchiveBo archiveRecord, MgkCmVideoRecord videoRecord);


    MaterialAggregationInfoBo toAggregationBo(MgkCmArchiveBo archiveRecord);


    MgkCmArchiveBo toBo(MgkCmArchiveRecord record);


}
