package com.bilibili.sycpb.cpm.scv.app.service.databus.v1.material_aggregation;

import com.bilibili.databus.core.DataBusClient;
import com.bilibili.discovery.DiscoveryClient;
import com.bilibili.sycpb.cpm.scv.app.service.databus.v1.watermark_erase.DatabusConfigSupport;
import com.bilibili.sycpb.cpm.scv.app.service.databus.v1.watermark_erase.DatabusProperty;
import com.bilibili.sycpb.cpm.scv.app.service.databus.v1.watermark_erase.DatabusProperty.Pub;
import com.bilibili.sycpb.cpm.scv.app.service.databus.v1.watermark_erase.WatermarkErasePipelineDatabusConfig.DatabusProperties;
import io.opentelemetry.api.OpenTelemetry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/5/7
 */
@Slf4j
@Configuration
public class VideoAggregationViewUpdateDatabusPubConfig implements DatabusConfigSupport {

    @Bean
    public DataBusClient videoAggregationViewUpdatePub(DatabusProperties manualWarpDatabusProperties,
            DiscoveryClient discoveryClient,
            OpenTelemetry discoveryNoopOpenTelemetry) {

        DatabusProperty property = manualWarpDatabusProperties.getProperties()
                .get("video-aggregation");

        Pub pubProperty = property.getPub();

        return createDatabusPubV3(
                property.getTopic(), pubProperty.getGroup(), pubProperty.getAppId(), pubProperty.getZone(),
                pubProperty.getToken(), discoveryClient, discoveryNoopOpenTelemetry
        );

    }


}
