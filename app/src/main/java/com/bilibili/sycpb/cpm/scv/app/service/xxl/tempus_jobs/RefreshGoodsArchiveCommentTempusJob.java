/*
 * Copyright (c) 2015-2023 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.service.xxl.tempus_jobs;

import com.alibaba.fastjson.JSON;
import com.bapis.ad.component.CommentComponentAuditStatus;
import com.bapis.ad.component.CommentComponentStatus;
import com.bapis.ad.component.ComponentType;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.AdDataSourceConfig;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.tables.TLauArchiveCommentConversionComponent;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.tables.records.LauArchiveCommentConversionComponentRecord;
import com.bilibili.sycpb.cpm.scv.app.service.rpc.CommunityRpcService;
import com.bilibili.sycpb.cpm.scv.app.utils.NumberUtils;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Result;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.tables.TLauArchiveCommentConversionComponent.LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT;

/**
 * 刷新商品稿件评论任务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RefreshGoodsArchiveCommentTempusJob implements BasicProcessor {

    public static final String ID = "RefreshGoodsArchiveCommentJob.execute";

    @Value("${comment.refresh.goods.batchSize:400}")
    private Integer batchSize;

    private final CommunityRpcService communityRpcService;

    @Resource(name = AdDataSourceConfig.AD_DSL_CONTEXT)
    private DSLContext ad;

    @Override
    public ProcessResult process(TaskContext context) throws Exception {
        OmsLogger logger = context.getOmsLogger();
        String param = Optional.ofNullable(context.getJobParams()).orElse("");

        logger.info("RefreshGoodsArchiveCommentTempusJob 开始执行, 参数: {}", param);
        log.info("RefreshGoodsArchiveCommentTempusJob 开始执行, 参数: {}", param);

        try {
            GoodsCommentRefreshBo goodsCommentRefreshBo = new GoodsCommentRefreshBo();
            if (StringUtils.isNotEmpty(param)) {
                goodsCommentRefreshBo = JSON.parseObject(param, GoodsCommentRefreshBo.class);
            }
            logger.info("解析参数: {}", JSON.toJSONString(goodsCommentRefreshBo));
            log.info("{}: parse, param, param={}", ID, JSON.toJSONString(goodsCommentRefreshBo));

            Long startId = goodsCommentRefreshBo.getMinId() != null ? goodsCommentRefreshBo.getMinId() : 0L;
            Long endId;

            Long maxId = goodsCommentRefreshBo.getMaxId();
            if (goodsCommentRefreshBo.getMaxId() == null) {
                maxId = fetchMaxId();
            }
            logger.info("获取最大ID: {}", maxId);
            log.info("{}: fetchMaxId, maxId={}", ID, maxId);

            while (true) {
                TimeUnit.MILLISECONDS.sleep(10);

                endId = startId + batchSize;
                final var records = fetchGoodsArchiveRecords(startId, endId, goodsCommentRefreshBo.getAids(), goodsCommentRefreshBo.getIds());
                if (CollectionUtils.isEmpty(records)) {
                    if (endId >= maxId) {
                        logger.info("处理完成, 结束循环, maxId: {}", maxId);
                        log.info("{}: fetchGoodsArchiveRecords,结束break,maxId={}", ID, maxId);
                        break;
                    }
                    startId = endId;
                    continue;
                }

                logger.info("处理一批记录, size: {}, maxId: {}, startId: {}, endId: {}",
                           records.size(), maxId, startId, endId);
                log.info("{}: fetchGoodsArchiveRecords,拉取一批,size={},maxId={},startId={},endId={}", ID, records.size(), maxId, startId, endId);

                for (LauArchiveCommentConversionComponentRecord record : records) {
                    if (!NumberUtils.isPositive(record.getAid())) {
                        continue;
                    }

                    final var commentDisabled = communityRpcService.isCommentDisabled(record.getAid());

                    // qps控制在10以下防止触发主站的频控
                    TimeUnit.MILLISECONDS.sleep(100);

                    // 禁用 & 非注册失败，则改成注册失败
                    if (commentDisabled && !Objects.equals(record.getStatus(), CommentComponentStatus.COMMENT_AREA_NOT_AVAILABLE_VALUE)) {
                        record.setStatus(CommentComponentStatus.COMMENT_AREA_NOT_AVAILABLE_VALUE)
                                .store();
                        logger.info("稿件评论区已关闭 - avid: {}, item_id: {}, comment_id: {}, id: {}",
                                   record.getAid(), record.getProductId(), record.getCommentId(), record.getId());
                        log.info("{}: avid={}, item_id={},comment_id={}对应的评论区已关闭,id={},maxId={}", ID, record.getAid(), record.getProductId(), record.getCommentId(),record.getId(),maxId);
                        continue;
                    }
                    final var commentId = communityRpcService.fetchTopCommentId(record.getAid());
                    if (Objects.equals(commentId, 0L)) {
                        record.setStatus(CommentComponentStatus.DELETED_VALUE)
                                .store();
                        logger.info("置顶评论已删除 - avid: {}, item_id: {}, comment_id: {}, id: {}",
                                   record.getAid(), record.getProductId(), record.getCommentId(), record.getId());
                        log.info("{}: avid={}, item_id={},comment_id={}对应的评论区置顶评论已删除,修改,id={},maxId={}", ID, record.getAid(), record.getProductId(), record.getCommentId(),record.getId(),maxId);
                    } else if (!Objects.equals(commentId, record.getCommentId())) {
                        record.setStatus(CommentComponentStatus.UN_TOP_VALUE)
                                .store();
                        logger.info("置顶评论已变更 - avid: {}, item_id: {}, comment_id: {} -> {}, id: {}",
                                   record.getAid(), record.getProductId(), record.getCommentId(), commentId, record.getId());
                        log.info("{}: avid={}, item_id={},comment_id={}对应的评论区置顶评论已变更,修改 -> {},id={},maxId={}", ID, record.getAid(), record.getProductId(), record.getCommentId(), commentId,record.getId(),maxId);
                    }
                }
                startId = endId;

                if (endId >= maxId) {
                    logger.info("处理完成, 结束循环, maxId: {}", maxId);
                    log.info("{}: fetchGoodsArchiveRecords,结束break,maxId={}", ID, maxId);
                    break;
                }
            }

            logger.info("RefreshGoodsArchiveCommentTempusJob 执行成功");
            log.info("RefreshGoodsArchiveCommentTempusJob 执行成功");
            return new ProcessResult(true, "success");
        } catch (Throwable t) {
            logger.error("RefreshGoodsArchiveCommentTempusJob 执行失败", t);
            log.error("RefreshGoodsArchiveCommentTempusJob 执行失败", t);
            return new ProcessResult(false, "执行失败: " + t.getMessage());
        }
    }

    private Long fetchMaxId() {
        Condition condition = LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.COMPONENT_TYPE.eq(ComponentType.GOODS_VALUE)
                .and(LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.AUDIT_STATUS.notEqual(CommentComponentAuditStatus.AUDIT_PASSED.getNumber()))
                .and(LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.STATUS.notEqual(CommentComponentStatus.DELETED_VALUE));
        Result<LauArchiveCommentConversionComponentRecord> result = ad.selectFrom(TLauArchiveCommentConversionComponent.LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT)
                .where(condition)
                .orderBy(TLauArchiveCommentConversionComponent.LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.ID.desc())
                .limit(1)
                .fetch();
        if (CollectionUtils.isEmpty(result)) {
            return 0L;
        }

        return result.get(0).getId();
    }

    private List<LauArchiveCommentConversionComponentRecord> fetchGoodsArchiveRecords(Long startId, Long endId, List<Long> aids, List<Long> ids) {
        Condition condition = LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.COMPONENT_TYPE.eq(ComponentType.GOODS_VALUE)
                .and(LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.AUDIT_STATUS.notEqual(CommentComponentAuditStatus.AUDIT_PASSED.getNumber()))
                .and(LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.STATUS.notEqual(CommentComponentStatus.DELETED_VALUE))
                .and(LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.ID.ge(startId))
                .and(LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.ID.lt(endId));

        if (!CollectionUtils.isEmpty(ids)) {
            condition.and(LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.ID.in(ids));
        }
        if (!CollectionUtils.isEmpty(aids)) {
            condition.and(LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.AID.in(aids));
        }

        return ad.fetch(LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT, condition);
    }

    @Data
    static class GoodsCommentRefreshBo {
        private List<Long> aids;
        private List<Long> ids;
        private Long minId;
        private Long maxId;
    }
}
