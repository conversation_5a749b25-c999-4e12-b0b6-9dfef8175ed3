/*
 * Copyright (c) 2015-2023 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2;

import com.bilibili.sycpb.cpm.scv.app.utils.constants.ArchiveState;
import com.google.common.collect.Lists;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import pleiades.component.ecode.ServerCode;

public class ArchivePipelineServerCodes {
    private static final Map<Integer, String> KVS = new HashMap<>();
    public static final ServerCode VIDEO_DOWNLOAD_FAILED = newInstance(-600, "视频文件下载失败");
    public static final ServerCode VIDEO_FILE_OVER_SIZE = newInstance(-6001, "视频文件过大");
    public static final ServerCode VIDEO_MD5_MISMATCH = newInstance(-6002, "参数中的视频md5和下载后的结果不匹配");
    public static final ServerCode XCODE_REQUEST_FAILED = newInstance(-602, "上传视频云转码失败");
    public static final ServerCode XCODE_INTERCEPTED_BY_MAIN_SITE = newInstance(-6021, "视频云转码被主站风控拦截");
    public static final ServerCode FETCH_COVER_FAILED = newInstance(-603, "获取封面失败");
    public static final ServerCode XCODE_QUERY_PREVIEW_FAILED = newInstance(-604, "查询UGC转码结果失败");
    public static final ServerCode XCODE_QUERY_META_FAILED = newInstance(-605, "查询UGC元数据失败");
    public static final ServerCode AUTH_FAILED = newInstance(-607, "用户授权失败");
    public static final ServerCode BILI_CREATE_ARCHIVE_FAILED = newInstance(-608, "调用主站投稿失败");
    public static final ServerCode UGC_CID_EXPIRED = newInstance(-6081, "cid超过48小时未投稿");
    public static final ServerCode UGC_DUP_TITLE = newInstance(-6082, "稿件标题重复");
    public static final ServerCode ARCHIVE_POST_PROCESS_FAILED = newInstance(-609, "投稿后处理失败");

    @Deprecated
    public static final ServerCode PROCESSING = newInstance(-1004, "进行中");
    @Deprecated
    public static final ServerCode WAITING_AUDIT = newInstance(-1000, "待审核");
    @Deprecated
    public static final ServerCode WAITING_AUTH = newInstance(-1002, "待授权");
    @Deprecated
    public static final ServerCode WAITING_SUBMIT = newInstance(-1005, "待投稿");
    @Deprecated
    public static final ServerCode FAILED = newInstance(-1003, "投稿失败");


    public static final ServerCode ERASING_WATERMARK = newInstance(-7001, "该视频水印处理中");

    public static final ServerCode FAIL_TO_ERASE_WATERMARK = newInstance(-7002, "水印处理异常，请重新上传");
    public static final ServerCode SUCCESS_TO_ERASE_WATERMARK = newInstance(-7003,
            "水印处理成功，提交处理视频上传，该记录被清理");




    public static boolean isBiliCidInvalidErr(int code) {
        return code == 21036 || code == 21015;
    }

    public static boolean isBiliDupTitleErr(int code) {
        return code == 21012;
    }

    private static ServerCode newInstance(int code, String message) {
        final var serverCode = ServerCode.create(code, message);
        KVS.put(code, message);
        return serverCode;
    }

    public static boolean isCmAuditStatusFailed(Integer x) {
        return Objects.equals(x, FAILED.getCode());
    }

    public static boolean isCmAuditStatusProcessing(Integer x) {
        return Objects.equals(x, PROCESSING.getCode());
    }

    public static boolean isCmArchiveState(int x) {
        return x <= -600;
    }

    public static String getDesc(int x) {
        if (isCmArchiveState(x)) return KVS.getOrDefault(x, "已失败");

        return ArchiveState.getValueOrUnknown(x);
    }


    /**
     * 获取失败的审核状态码，其他都认为审核中
     *
     * @return
     */
    public static List<Integer> fetchFailedAuditStatusCode() {

        return Lists.newArrayList(
                VIDEO_DOWNLOAD_FAILED,
                VIDEO_FILE_OVER_SIZE,
                VIDEO_MD5_MISMATCH,
                XCODE_REQUEST_FAILED,
                XCODE_INTERCEPTED_BY_MAIN_SITE,
                FETCH_COVER_FAILED,
                XCODE_QUERY_PREVIEW_FAILED,
                XCODE_QUERY_META_FAILED,
                AUTH_FAILED,
                BILI_CREATE_ARCHIVE_FAILED,
                UGC_CID_EXPIRED,
                UGC_DUP_TITLE,
                ARCHIVE_POST_PROCESS_FAILED,
//            PROCESSING,
//            WAITING_AUDIT,
//            WAITING_AUTH,
//            WAITING_SUBMIT,
                FAILED,
//            ERASING_WATERMARK,
                FAIL_TO_ERASE_WATERMARK
//            SUCCESS_TO_ERASE_WATERMARK
        ).stream().map(ServerCode::getCode).collect(Collectors.toList());


    }
}
