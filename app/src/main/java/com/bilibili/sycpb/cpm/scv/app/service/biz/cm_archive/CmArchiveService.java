/*
 * Copyright (c) 2015-2021 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive;

import static com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.generated.Tables.MGK_CM_ARCHIVE;

import com.bapis.ad.mgk.material.MaterialAggregationViewPageReq;
import com.bapis.ad.mgk.material.MaterialAggregationViewPageResp;
import com.bapis.ad.mgk.material.MaterialAggregationViewServiceGrpc;
import com.bilibili.sycpb.cpm.scv.app.controller.open.lib.archive.bos.AuthBo;
import com.bilibili.sycpb.cpm.scv.app.controller.open.lib.archive.bos.SaveArchiveReqBo;
import com.bilibili.sycpb.cpm.scv.app.service.bfs.BfsService;
import com.bilibili.sycpb.cpm.scv.app.service.bili.archive.BiliArchiveService;
import com.bilibili.sycpb.cpm.scv.app.service.bili.bvcflow.BiliBvcFlowService;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.ArchivePipelineServerCodes;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.ArchivePipelineStatus;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.MaterialAggregationViewUpdateEventPublisher;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm.CmArchiveConverter;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm.CmArchivePipelineService;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm.CmArchivePipelineStage;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm.MgkArchiveRecordExtra;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.model.ArchiveModelConvertor;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.ArchiveServiceMaterialDelegate.ArchiveUpdateEvent;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.CmArchiveBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.CmArchiveListConditionBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.CmArchiveListConditionV3Bo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.CmArchiveProgressBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.CmArchiveV3Bo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.CoverBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.MgkCmArchiveBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.PreUploadBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.SaveArchiveRespBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.UploadFileDto;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.VideoDetailBo;
import com.bilibili.sycpb.cpm.scv.app.service.boss.BossService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.account.AccountService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.LaunchArchiveService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.MgkCmArchiveAccountMappingDaoService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.MgkCmArchiveDaoService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.MgkCmSpaceDaoService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.MgkCmVideoDaoService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.MgkDataSourceConfig;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.generated.tables.records.MgkCmArchiveAccountMappingRecord;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.generated.tables.records.MgkCmArchiveRecord;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.generated.tables.records.MgkCmVideoRecord;
import com.bilibili.sycpb.cpm.scv.app.service.pipeline.PipelineService;
import com.bilibili.sycpb.cpm.scv.app.service.rpc.AccountRpcService;
import com.bilibili.sycpb.cpm.scv.app.utils.FunctionUtils;
import com.bilibili.sycpb.cpm.scv.app.utils.JsonUtil;
import com.bilibili.sycpb.cpm.scv.app.utils.NumberUtils;
import com.bilibili.sycpb.cpm.scv.app.utils.Pager;
import com.bilibili.sycpb.cpm.scv.app.utils.constants.ArchiveMode;
import com.bilibili.sycpb.cpm.scv.app.utils.constants.AuthStatus;
import com.bilibili.sycpb.cpm.scv.app.utils.constants.MediaDataSourceType;
import com.bilibili.sycpb.cpm.scv.app.utils.constants.XcodeStatus;
import com.bilibili.sycpb.cpm.scv.app.utils.enums.ArchiveCoverOriginEnum;
import com.bilibili.sycpb.cpm.scv.app.utils.enums.ArchiveSourceEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Streams;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import java.io.InputStream;
import java.sql.Timestamp;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.IOUtils;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import pleiades.component.ecode.exception.ServerException;
import pleiades.component.http.client.response.BiliDataApiResponse;
import pleiades.venus.starter.rpc.client.RPCClient;

@Slf4j
@Service
@RequiredArgsConstructor
public class CmArchiveService {
    public static final String CM_ARCHIVE_BOSS_BUCKET_KEY = "cm-archive";

    private final MgkCmArchiveAccountMappingDaoService mgkCmArchiveAccountMappingDaoService;
    private final MgkCmVideoDaoService mgkCmVideoDaoService;
    private final MgkCmArchiveDaoService mgkCmArchiveDaoService;
    private final MgkCmSpaceDaoService mgkCmSpaceDaoService;
    private final BossService bossService;
    private final CmSpaceService cmSpaceService;
    private final BfsService bfsService;
    private final BiliArchiveService biliArchiveService;
    private final AccountService accountService;
    private final AccountRpcService accountRpcService;
    private final LaunchArchiveService launchArchiveService;
    private final BiliBvcFlowService biliBvcFlowService;
    private final CmArchiveComparator cmArchiveComparator;
    private final CmArchiveBoComparator cmArchiveBoComparator;
    private final CmArchivePipelineService cmArchivePipelineService;
    private final PipelineService pipelineService;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final ArchiveServiceMaterialDelegate archiveServiceMaterialDelegate;
    private final CmArchiveCommentService cmArchiveCommentService;
    private final MaterialAggregationViewUpdateEventPublisher aggregationViewUpdateEventPublisher;

    @Resource(name = MgkDataSourceConfig.MGK_DSL_CONTEXT)
    private DSLContext mgk;

    @RPCClient("sycpb.cpm.mgk-portal")
    private MaterialAggregationViewServiceGrpc.MaterialAggregationViewServiceBlockingStub materialAggregationViewServiceBlockingStub;



    private MgkCmArchiveRecord fetchKey(String key) {
        final var cid = NumberUtils.reverseRadix62(key);
        final var mgkCmArchiveRecord = mgkCmArchiveDaoService.fetchByCid(cid);
        Assert.notNull(mgkCmArchiveRecord, "对应的数据不存在");

        return mgkCmArchiveRecord;
    }

    @Transactional(MgkDataSourceConfig.MGK_TX_MGR)
    public void authPass(AuthBo authBo) {
        final var mgkCmArchiveRecord = fetchKey(authBo.getKey());
        final var authStatus = mgkCmArchiveRecord.getAuthStatus();
        if (AuthStatus.isRejected(authStatus)) {
            return;
        }

        if (AuthStatus.isWaiting(authStatus)) {
            mgkCmArchiveRecord.setAuthStatus(AuthStatus.SUCCEEDED)
                .setAuthTime(Timestamp.from(Instant.now()))
                .store();
            if (Objects.nonNull(authBo.getAuthFree()) && authBo.getAuthFree()) {
                launchArchiveService.auth(mgkCmArchiveRecord.getMid(), mgkCmArchiveRecord.getAccountId());
            }
            this.tryNotifyArchiveOnAudit(mgkCmArchiveRecord);
        }
        if (!NumberUtils.isPositive(mgkCmArchiveRecord.getAvid())) {
            final var ctx = cmArchivePipelineService.genPostAuthContext(mgkCmArchiveRecord);
            cmArchivePipelineService.triggerCmArchivePipeline(ctx);
        }
    }

    public void authReject(AuthBo authBo) {
        final var mgkCmArchiveRecord = fetchKey(authBo.getKey());
        // 之前已经有过操作, 不能再操作了
        if (AuthStatus.isAuthDone(mgkCmArchiveRecord.getAuthStatus())) {
            return;
        }

        mgkCmArchiveRecord.setAuthStatus(AuthStatus.REJECTED)
            .setAuditStatus(ArchivePipelineServerCodes.FAILED.getCode())
            .setAuditReason("用户驳回")
            .setAuthTime(Timestamp.from(Instant.now()))
            .store();

        this.tryNotifyArchiveOnAudit(mgkCmArchiveRecord);
    }

    private void tryNotifyArchiveOnAudit(MgkCmArchiveRecord record) {

        Try.run(() -> {

            aggregationViewUpdateEventPublisher.onRecordUpdated(record, null);
        }).onFailure(t -> {
            log.error("Fail to tryNotifyArchiveOnAudit, recordMd5={}, avid={} ", record.getVideoMd5(),
                    record.getAvid(), t);
        });

    }

    /**
     * @param cid ugc视频物料id
     */
    public VideoDetailBo fetchVideoInfo(Long cid) {
        final var mgkCmArchiveRecord = mgkCmArchiveDaoService.fetchByCid(cid);
        return fetchVideoInfo(mgkCmArchiveRecord);
    }

    private VideoDetailBo fetchVideoInfo(MgkCmArchiveRecord mgkCmArchiveRecord) {
        var accountId = mgkCmArchiveRecord.getAccountId();
        if (!NumberUtils.isPositive(accountId)) {
            final var mgkCmSpaceRecord = mgkCmSpaceDaoService.fetchSpaceByMid(mgkCmArchiveRecord.getMid());
            accountId = mgkCmSpaceRecord.getAccountId();
            mgkCmArchiveRecord.setAccountId(accountId)
                .store();
        }
        final var accAccountRecord = accountService.fetch(accountId);
        final var biliCategory = biliArchiveService.getBiliCategory(mgkCmArchiveRecord.getMid());
        String catOneDesc = null;
        String catTwoDesc = null;
        for (var parent : biliCategory) {
            if (Objects.equals(parent.getId(), mgkCmArchiveRecord.getCatOne())) {
                catOneDesc = parent.getName();
                for (var child : parent.getChildren()) {
                    if (Objects.equals(child.getId(), mgkCmArchiveRecord.getCatTwo())) {
                        catTwoDesc = child.getName();
                        break;
                    }
                }
                break;
            }
        }
        final var biliAccountInfoBo = accountRpcService.fetchInfo(mgkCmArchiveRecord.getMid());
        final var isAuthDone = AuthStatus.isAuthDone(mgkCmArchiveRecord.getAuthStatus());
        var publicEncodedUrl = mgkCmArchiveRecord.getPublicEncodedUrl();
        // 稿件的公网可见链接有效期很短, 可能需要在请求的时候刷新, 并保存刷新的结果, 最多保留30天
        if (StringUtils.hasText(publicEncodedUrl) && BiliBvcFlowService.isDeadlineMet(publicEncodedUrl)) {
            final var bvcFlowInfo = FunctionUtils.execWithRetires(BiliBvcFlowService.ID, () -> biliBvcFlowService.fetchBvcFlowInfo(mgkCmArchiveRecord.getUgcFilename()), 3, Duration.ofMillis(150));
            Assert.notNull(bvcFlowInfo, "调用视频云获取视频信息失败");

            publicEncodedUrl = bvcFlowInfo.getPublicSignedUrl();
            mgkCmArchiveRecord.setPublicEncodedUrl(publicEncodedUrl)
                .store();
        }
        final long authTime;
        if (NumberUtils.isZeroValueTimestamp(mgkCmArchiveRecord.getAuthTime())) {
            authTime = 0L;
        } else {
            authTime = mgkCmArchiveRecord.getAuthTime().getTime();
        }
        return VideoDetailBo.builder()
            .agentName(accountService.fetchAgentName(accAccountRecord.getDependencyAgentId()))
            .companyName(accountService.fetchCustomerName(accAccountRecord.getCustomerId()))
            .mid(mgkCmArchiveRecord.getMid())
            .nickName(biliAccountInfoBo.getNickName())
            .coverUrl(mgkCmArchiveRecord.getCoverUrl())
            .coverMd5(mgkCmArchiveRecord.getCoverMd5())
            .videoUrl(publicEncodedUrl)
            .title(mgkCmArchiveRecord.getTitle())
            .tags(Arrays.stream(mgkCmArchiveRecord.getTags().split(",")).collect(Collectors.toList()))
            .catOne(mgkCmArchiveRecord.getCatOne())
            .catOneDesc(catOneDesc)
            .catTwo(mgkCmArchiveRecord.getCatTwo())
            .catTwoDesc(catTwoDesc)
            .authFree(isAuthDone)
            .authTime(authTime)
            .build();
    }

    public VideoDetailBo fetchVideoInfoForAudit(String key) {
        final var mgkCmArchiveRecord = fetchKey(key);
        return fetchVideoInfo(mgkCmArchiveRecord);
    }

    /**
     * 手动上传封面
     */
    @SneakyThrows
    public CoverBo uploadCover(UploadFileDto uploadFileDto) {
        InputStream inputStream = uploadFileDto.getInputStream();
        String rawFileName = uploadFileDto.getFileName();

        final var bytes = IOUtils.toByteArray(inputStream);
        final var md5 = DigestUtils.md5Hex(bytes);
        final String ext;
        if (StringUtils.hasText(rawFileName) && rawFileName.contains(".")) {
            final var splits = rawFileName.split("\\.");
            ext = splits[splits.length - 1];
        } else {
            ext = null;
        }

        String contentType = uploadFileDto.getContentType();

        // 封面上传到 bfs
        final var url = bfsService.upload("cm/cover", md5, ext, contentType, bytes);
        return CoverBo.builder()
            .url(url)
            .md5(md5)
            .build();
    }

    /**
     * 根据 objectKey 生成视频上传 url
     */
    public String genVideoUploadUrl(String objectKey) {
        return bossService.genPreSignedUrl(CM_ARCHIVE_BOSS_BUCKET_KEY, objectKey);
    }

    // 无套路, 传什么参数返回什么数据
    public List<CmArchiveBo> simpleList(CmArchiveListConditionBo conditionBo) {
        final var accountId = conditionBo.getAccountId();
        if (ArchiveMode.isCmSpace(conditionBo.getArchiveMode())) {
            // 有些历史数据中account_id字段没值, 暂时过滤掉
            conditionBo.setAccountId(null);
            final var mgkCmSpaceRecord = mgkCmSpaceDaoService.fetchSpace(accountId);
            if (Objects.nonNull(mgkCmSpaceRecord)) {
                final var mid = mgkCmSpaceRecord.getMid();
                conditionBo.setMid(mid);
            }
        }
        final var cmArchiveRecords = mgkCmArchiveDaoService.list(conditionBo);
        if (CollectionUtils.isEmpty(cmArchiveRecords)) {
            return Collections.emptyList();
        }

        final var rawMd5s = cmArchiveRecords.stream()
            .map(MgkCmArchiveRecord::getVideoMd5)
            .distinct()
            .collect(Collectors.toList());
        final var cmVideoRecordMap = mgkCmVideoDaoService.fetchByMd5s(rawMd5s)
            .stream()
            .collect(Collectors.toMap(MgkCmVideoRecord::getMd5, Function.identity()));
        return cmArchiveRecords.stream()
            .sorted(new CmArchiveComparator())
            .map(x -> CmArchiveBo.fromRecords(x, cmVideoRecordMap.get(x.getVideoMd5()), null))
            .collect(Collectors.toList());
    }

    /*
商业账户态查询：

com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.CmArchiveService#listV3
{"accountIds":[10010], "pageNo":1, "pageSize":100, "isAuditPassed":"true", "orderBy":"pubTime+desc", "isDeleted":0 ,"archiveMode":0}

com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.CmArchiveService#listV3
{"accountIdsInAggregationView":[10010], "pageNo":1, "pageSize":100, "isAuditPassed":"true", "orderBy":"pubTime+desc", "isDeleted":0 ,"archiveMode":0}

     */
    public Pager<CmArchiveV3Bo> listV3(CmArchiveListConditionV3Bo conditionBo) {

        Pager<MgkCmArchiveBo> pagedMgkCmArchive;
        if (CollectionUtils.isEmpty(conditionBo.getAccountIdsInAggregationView())) {
            // 个人账户态查询视角
            pagedMgkCmArchive = selectAllInMysqlAndPageInMemory(conditionBo);
        } else {
            // 代理商账号态查询视角
            pagedMgkCmArchive = pageAggregationViewInEs(conditionBo);
        }

        return this.mappingToCmArchiveAndPendingCmVideo(pagedMgkCmArchive);
    }

    /**
     * 回表 回表
     */
    private Pager<CmArchiveV3Bo> mappingToCmArchiveAndPendingCmVideo(Pager<MgkCmArchiveBo> pagedMgkCmArchive) {

        final var truncatedRecords = pagedMgkCmArchive.getData();
        // 提取稿件 md5
        final var rawMd5s = truncatedRecords.stream()
                .map(MgkCmArchiveBo::getVideoMd5)
                .distinct()
                .collect(Collectors.toList());
        // 获取商业视频素材
        // 只对当前页内的数据进行回表
        List<MgkCmVideoRecord> mgkCmVideoRecords = mgkCmVideoDaoService.fetchByMd5s(rawMd5s);

        Map<String, MgkCmVideoRecord> cmVideoRecordMap = mgkCmVideoRecords.stream()
                .collect(Collectors.toMap(MgkCmVideoRecord::getMd5, Function.identity()));

        if (CollectionUtils.isEmpty(cmVideoRecordMap)) {
            return Pager.empty();
        }

//        // 稿件记录遍历，拼接其他信息
        Pager<CmArchiveV3Bo> page = Pager.convert(pagedMgkCmArchive, x -> {
            final var mgkCmVideoRecord = cmVideoRecordMap.get(x.getVideoMd5());
            if (Objects.isNull(mgkCmVideoRecord)) {
                return null;
            }

            return CmArchiveConverter.MAPPER.toCmArchiveBo(x, mgkCmVideoRecord);
        });

        archiveServiceMaterialDelegate.fillingMaterialIdOfArchive(page.getData());

        return page;
    }

    private Pager<MgkCmArchiveBo> selectAllInMysqlAndPageInMemory(CmArchiveListConditionV3Bo conditionBo) {

        if (StringUtils.hasText(conditionBo.getName())) {

            if (!StringUtils.hasText(conditionBo.getMaterialSearchWord())) {
                // upgrade: using material-search-word to filter
                conditionBo.setMaterialSearchWord(conditionBo.getName());
            } else {
                // name为过时字段
                log.warn("Skip condition name in listCmArchive, name and material-search-word are both set, name will "
                        + "be ignored");

            }

        }
        List<MgkCmArchiveBo> records = this.listRawRecords(conditionBo);
        if (CollectionUtils.isEmpty(records)) {
            return Pager.empty();
        }

        records = archiveServiceMaterialDelegate.filterByMaterialSearchWord(records, conditionBo);

        Pager<MgkCmArchiveBo> pagedRecords = Pager.page(records, conditionBo.getPageNo(), conditionBo.getPageSize());

        return pagedRecords;


    }


    private Pager<MgkCmArchiveBo> pageAggregationViewInEs(CmArchiveListConditionV3Bo conditionBo) {

        return Try.of(() -> {
            MaterialAggregationViewPageReq aggregationViewPageReq = ArchiveModelConvertor.instance
                    .toAggregationViewPageReq(conditionBo);

            MaterialAggregationViewPageResp page = materialAggregationViewServiceBlockingStub.pageAggregationView(
                    aggregationViewPageReq);

            return new Pager<MgkCmArchiveBo>(
                    page.getPage(),
                    page.getTotal(),
                    page.getDataList().stream().map(archive -> {
                        return ArchiveModelConvertor.instance.aggregationViewToArchiveRecord(archive);
                    }).collect(Collectors.toList())

            );

        }).getOrElseThrow(t -> {

            log.error("Fail to pageAggregationViewInEs, conditionBo={}", conditionBo, t);
            return new RuntimeException("查询视频素材失败", t);
        });

    }





    @Deprecated
    public Pager<CmArchiveBo> list(CmArchiveListConditionBo conditionBo) {
        List<MgkCmVideoRecord> mgkCmVideoRecords = null;
        if (StringUtils.hasText(conditionBo.getName())) {
            // 文件名不在一张表里, 需要先查
            mgkCmVideoRecords = mgkCmVideoDaoService.fetchByName(conditionBo.getName());
            if (CollectionUtils.isEmpty(mgkCmVideoRecords)) {
                return Pager.empty();
            }

            final var videoMd5s = mgkCmVideoRecords.stream()
                .map(MgkCmVideoRecord::getMd5)
                .collect(Collectors.toList());
            conditionBo.setMd5s(videoMd5s);
        }
        final var records = listRawRecords(conditionBo);
        if (CollectionUtils.isEmpty(records)) {
            return Pager.empty();
        }

        records.sort(cmArchiveComparator);
        final var pagedRecords = Pager.page(records, conditionBo.getPageNo(), conditionBo.getPageSize());
        final var truncatedRecords = pagedRecords.getData();
        // 提取稿件 md5
        final var rawMd5s = truncatedRecords.stream()
            .map(MgkCmArchiveRecord::getVideoMd5)
            .distinct()
            .collect(Collectors.toList());
        // 获取商业视频素材
        if (Objects.isNull(mgkCmVideoRecords)) {
            mgkCmVideoRecords = mgkCmVideoDaoService.fetchByMd5s(rawMd5s);
        }
        final var cmVideoRecordMap = mgkCmVideoRecords.stream()
            .collect(Collectors.toMap(MgkCmVideoRecord::getMd5, Function.identity()));
        if (CollectionUtils.isEmpty(cmVideoRecordMap)) {
            return Pager.empty();
        }

        // 稿件记录遍历，拼接其他信息
        Function<MgkCmArchiveRecord, CmArchiveBo> convertFunc = x -> {
            final var mgkCmVideoRecord = cmVideoRecordMap.get(x.getVideoMd5());
            if (Objects.isNull(mgkCmVideoRecord)) {
                return null;
            }

            return CmArchiveBo.fromRecords(x, mgkCmVideoRecord, null);
        };
        return Pager.convert(pagedRecords, convertFunc);
    }

    private List<MgkCmArchiveBo> listRawRecords(CmArchiveListConditionV3Bo conditionBo) {
        final List<MgkCmArchiveBo> records = new ArrayList<>();

        if (!CollectionUtils.isEmpty(conditionBo.getAvids())) {
            Assert.isTrue(conditionBo.getAvids().size() <= 10, "搜索稿件avid 最大10个");
        }

        final var hasSelf = Objects.isNull(conditionBo.getIsShared()) || !conditionBo.getIsShared();
        final var hasShared = Objects.isNull(conditionBo.getIsShared()) || conditionBo.getIsShared();
        if (hasSelf) {
            records.addAll(mgkCmArchiveDaoService.list(conditionBo).stream()
                    .map(CmArchiveConverter.MAPPER::toBo)
                    .map(bo->bo.setArchiveSource(ArchiveSourceEnum.CREATE.getCode()))
                    .collect(Collectors.toList()));
        }
        if (hasShared) {
            // 暂时只支持查看单账号推送
            final var avids =
                mgkCmArchiveAccountMappingDaoService.list(Lists.newArrayList(conditionBo.getAccountIds().get(0)), conditionBo.getAvids())
                .stream()
                .map(MgkCmArchiveAccountMappingRecord::getAvid)
                .distinct()
                .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(avids)) {
                // 推送的avid不属于当前查询账号
                conditionBo.setAccountIds(null);
                conditionBo.setAvids(avids);
                // 可能这个推送的稿件已经被原账号删除了
                conditionBo.setIsDeleted(null);
                records.addAll(mgkCmArchiveDaoService.list(conditionBo).stream()
                        .map(CmArchiveConverter.MAPPER::toBo)
                        .map(bo->bo.setArchiveSource(ArchiveSourceEnum.PUSH.getCode()))
                        .collect(Collectors.toList()));
            }
        }

        return records.stream()
                .sorted(this.postSort(
                        conditionBo.getOrderBy(),
                        records.stream().map(r -> r.getAuditStatus() >= 0).reduce((a, b) -> a && b).orElse(false)
                ))
                .collect(Collectors.toList());

    }

    /**
     * {@link MgkCmArchiveDaoService#getOrderBy(String)}
     *
     * @param orderBy
     * @param isAllAuditPassed
     * @return
     */
    public Comparator<MgkCmArchiveBo> postSort(String orderBy, boolean isAllAuditPassed) {

        boolean desc = orderBy.contains("desc");

        if (isAllAuditPassed) {

            return (o1, o2) -> {
                if (orderBy.contains("pub")) {
                    if (desc) {
                        return o2.getAuditPassTime().compareTo(o1.getAuditPassTime());
                    } else {
                        return o1.getAuditPassTime().compareTo(o2.getAuditPassTime());
                    }
                } else if (orderBy.contains("ctime")) {
                    if (desc) {
                        return o2.getCtime().compareTo(o1.getCtime());
                    } else {
                        return o1.getCtime().compareTo(o2.getCtime());
                    }
                } else {
                    if (desc) {
                        return o2.getMtime().compareTo(o1.getMtime());
                    } else {
                        return o1.getMtime().compareTo(o2.getMtime());
                    }
                }
            };

        } else {

            return (o1, o2) -> {
                if (orderBy.contains("pub") || orderBy.contains("ctime")) {
                    if (desc) {
                        return o2.getCtime().compareTo(o1.getCtime());
                    } else {
                        return o1.getCtime().compareTo(o2.getCtime());
                    }
                } else {
                    if (desc) {
                        return o2.getMtime().compareTo(o1.getMtime());
                    } else {
                        return o1.getMtime().compareTo(o2.getMtime());
                    }
                }
            };

        }

    }

    private List<MgkCmArchiveRecord> listRawRecords(CmArchiveListConditionBo conditionBo) {
        final var accountId = conditionBo.getAccountId();
        // 获取稿件记录
        List<MgkCmArchiveRecord> cmArchiveRecords = new ArrayList<>();
        if (ArchiveMode.isUserSpace(conditionBo.getArchiveMode())) {
            conditionBo.setAuditPass(false);
            cmArchiveRecords = mgkCmArchiveDaoService.list(conditionBo);
        } else {
            conditionBo.setAccountId(null);
            // 本账号包含的部分
            if (MediaDataSourceType.includeSelf(conditionBo.getDataSourceType())) {
                final var mgkCmSpaceRecord = mgkCmSpaceDaoService.fetchSpace(accountId);
                if (Objects.nonNull(mgkCmSpaceRecord)) {
                    final var mid = mgkCmSpaceRecord.getMid();
                    conditionBo.setMid(mid);
                    cmArchiveRecords.addAll(mgkCmArchiveDaoService.list(conditionBo));
                }
            }
            // 推送的部分
            if (MediaDataSourceType.includePush(conditionBo.getDataSourceType())) {
                final var mappingRecords = mgkCmArchiveAccountMappingDaoService.list(accountId);
                if (!CollectionUtils.isEmpty(mappingRecords)) {
                    final var avids = mappingRecords.stream()
                        .map(MgkCmArchiveAccountMappingRecord::getAvid)
                        .distinct()
                        .collect(Collectors.toList());
                    conditionBo.setAvids(avids);
                    conditionBo.setMid(null);
                    // 可能这个推送的稿件已经被原账号删除了
                    conditionBo.setIsDeleted(null);
                    cmArchiveRecords.addAll(mgkCmArchiveDaoService.list(conditionBo));
                }
            }
        }
        return cmArchiveRecords;
    }

    /**
     * 批量删除稿件
     *
     * @param accountId
     * @param cids
     */
    @Transactional(MgkDataSourceConfig.MGK_TX_MGR)
    public void batchDelete(Integer accountId, List<Long> cids) {
        for (final var cid : cids) {
            final var record = mgkCmArchiveDaoService.fetchByCid(cid);
            // 跳过不存在的数据
            if (Objects.isNull(record)) {
                continue;
            }
            // 删除本账号的推送
            mgkCmArchiveAccountMappingDaoService.delete(accountId, record.getAvid());
            Assert.isTrue(Objects.equals(accountId, record.getAccountId()), "不能删除非本账号的数据");
            Assert.isTrue(!NumberUtils.isPositive(record.getAvid()), "已经投稿成功的视频无法删除: avid=" + record.getAvid() + ", 请联系主站审核同学对该稿件做驳回处理");
            // 真删除
            record.delete();
        }
    }

    /**
     * 上传稿件
     */
    public void updateArchive(SaveArchiveReqBo saveArchiveReqBo) {

        // 获取稿件记录
        final var mgkCmArchiveRecord = mgkCmArchiveDaoService.fetchByCid(saveArchiveReqBo.getCid());
        final var title = saveArchiveReqBo.getTitle();
        CmArchivePipelineService.validateTitle(title);

        if (ArchiveMode.isUserSpace(mgkCmArchiveRecord.getArchiveMode())) {
            final var biliVideoTags = saveArchiveReqBo.getBiliVideoTags();
            Assert.isTrue(!CollectionUtils.isEmpty(biliVideoTags) && biliVideoTags.size() <= 10, "视频包含的标签数至少1个至多10个");

            biliVideoTags.forEach(x -> Assert.isTrue(!x.contains(","), "标签不能包含逗号"));
            final var textBiliVideoTags = String.join(",", biliVideoTags);
            Assert.isTrue(textBiliVideoTags.length() < 256, "视频标签的内容过长");

            mgkCmArchiveRecord.setTags(textBiliVideoTags);
        }
        final var cover = saveArchiveReqBo.getCover();
        Assert.isTrue(Objects.nonNull(cover) && StringUtils.hasText(cover.getUrl()) && StringUtils.hasText(cover.getMd5()), "视频封面不能为空");

        mgkCmArchiveRecord.setCoverUrl(cover.getUrl())
            .setCoverMd5(cover.getMd5());
        mgkCmArchiveRecord.setTitle(title);
        Assert.isTrue(mgkCmArchiveRecord.changed(), "视频内容没有任何改变");

        if (NumberUtils.isPositive(mgkCmArchiveRecord.getAvid()) && Objects.equals(mgkCmArchiveRecord.getAuthStatus(), AuthStatus.SUCCEEDED)) {
            // 只有授权且投稿了才能编辑
            biliArchiveService.updateArchive(mgkCmArchiveRecord.getMid(), mgkCmArchiveRecord.getAvid(), mgkCmArchiveRecord.getCatTwo(), mgkCmArchiveRecord.getCoverUrl(), mgkCmArchiveRecord.getTitle(), mgkCmArchiveRecord.getTags());
        }
        mgkCmArchiveRecord.store();

        applicationEventPublisher.publishEvent(new ArchiveUpdateEvent(this).setRecord(mgkCmArchiveRecord));
    }

    public SaveArchiveRespBo saveArchive(SaveArchiveReqBo saveArchiveReqBo) {
        final var record = Try.of(() -> {

            if (NumberUtils.isPositive(saveArchiveReqBo.getCid())) {
                return mgkCmArchiveDaoService.fetchByCid(saveArchiveReqBo.getCid());
            } else if (org.apache.commons.lang3.StringUtils.isNotEmpty(saveArchiveReqBo.getPipelineId())) {
                return mgkCmArchiveDaoService.fetchExistingRecord(saveArchiveReqBo.getPipelineId());
            } else {
                return null;
            }
        }).get();

        Assert.notNull(record, "找不到对应的稿件数据");
        Assert.isTrue(Objects.equals(record.getAvid(), 0L), "对应的稿件已存在: avid=" + record.getAvid());
        final var cover = saveArchiveReqBo.getCover();
        if (Objects.nonNull(cover) && StringUtils.hasText(cover.getUrl())) {
            Assert.isTrue(StringUtils.hasText(cover.getMd5()), "封面md5不能为空");
            record.setCoverUrl(cover.getUrl())
                    .setCoverMd5(cover.getMd5())
                    .setCoverOrigin(Optional.ofNullable(cover.getSource())
                            .orElse(ArchiveCoverOriginEnum.CUSTOM.getCode()));

        }
        if (ArchiveMode.isUserSpace(record.getArchiveMode())) {
            Assert.isTrue(NumberUtils.isPositive(saveArchiveReqBo.getBiliCatOne()) && NumberUtils.isPositive(saveArchiveReqBo.getBiliCatTwo()), "未指定稿件分区");
            record.setCatOne(saveArchiveReqBo.getBiliCatOne())
                .setCatTwo(saveArchiveReqBo.getBiliCatTwo());
            CmArchivePipelineService.validateTags(saveArchiveReqBo.getBiliVideoTags());
            record.setTags(String.join(",", saveArchiveReqBo.getBiliVideoTags()));
        }
        CmArchivePipelineService.validateTitle(saveArchiveReqBo.getTitle());
        record.setTitle(saveArchiveReqBo.getTitle());

        final var ctx = cmArchivePipelineService.genPostTwoStageSubmitContext(record);
        ctx.setEnableSelectiveComment(saveArchiveReqBo.getEnableSelectiveComment());
        cmArchivePipelineService.triggerCmArchivePipeline(ctx);
        return SaveArchiveRespBo.builder()
            .avid(record.getAvid().toString())
            .build();
    }

    public CmArchiveProgressBo progress(String pipelineId) {
        var record = mgkCmArchiveDaoService.fetchExistingRecord(pipelineId);
        if (Objects.isNull(record)) {
            record = fetchLegacy(pipelineId);
            Assert.notNull(record, "找不到pipeline_id=" + pipelineId + "对应的数据");
        }
        return fetchProgress(record, pipelineId);
    }

    private CmArchiveProgressBo fetchProgress(MgkCmArchiveRecord record, String pipelineId) {
        final var builder = CmArchiveProgressBo.builder()
            .accountId(record.getAccountId())
            .videoMd5(record.getVideoMd5())
            .pipelineId(pipelineId);
        boolean isErr = false, isTerm = false;
        final String msg;
        if (StringUtils.hasText(record.getUniqueId())) {
            msg = ArchivePipelineServerCodes.getDesc(record.getAuditStatus());
            isErr = ArchivePipelineStatus.isFailed(record.getPipelineStatus());
            isTerm = !ArchivePipelineStatus.isProcessing(record.getPipelineStatus());
        } else {
            log.info("progress: legacy branch");
            final var xcodeStatus = record.getXcodeStatus();
            final var authStatus = record.getAuthStatus();
            final var auditStatus = record.getAuditStatus();
            if (XcodeStatus.isWaiting(xcodeStatus) || XcodeStatus.isFailed(xcodeStatus)) {
                msg = XcodeStatus.desc(xcodeStatus);
                if (XcodeStatus.isFailed(xcodeStatus)) {
                    isErr = true;
                    isTerm = true;
                }
            } else if (AuthStatus.isWaiting(authStatus) || AuthStatus.isRejected(authStatus)) {
                msg = AuthStatus.desc(authStatus);
                isTerm = true;
                if (AuthStatus.isRejected(authStatus)) {
                    isErr = true;
                }
            } else {
                msg = ArchivePipelineServerCodes.getDesc(auditStatus);
                if (NumberUtils.isNonNegative(auditStatus)) {
                    isTerm = true;
                } else if (!ArchivePipelineServerCodes.isCmArchiveState(auditStatus)) {
                    isTerm = true;
                    isErr = true;
                } else {
                    if (!ArchivePipelineServerCodes.isCmAuditStatusProcessing(auditStatus)) {
                        isTerm = true;
                    }
                    if (ArchivePipelineServerCodes.isCmAuditStatusFailed(auditStatus)) {
                        isErr = true;
                    }
                }
            }
        }
        return builder.msg(msg)
            .errorMsg(msg)
            .isTerm(isTerm)
            .isErr(isErr)
            .remaining(isErr ? -1 : isTerm ? 0 : 1)
            .build();
    }

    public CmArchiveProgressBo progress(Integer accountId, String videoMd5) {
        final var record = mgkCmArchiveDaoService.fetchCmArchive(accountId, videoMd5);
        Assert.notNull(record, "找不到md5=" + videoMd5 + ", account_id=" + accountId + "对应的数据");
        return fetchProgress(record, record.getUniqueId());
    }

    public PreUploadBo fetchPipelineStage0Result(String pipelineId) {
        final var record = mgkCmArchiveDaoService.fetchExistingRecord(pipelineId);
        if (Objects.isNull(record)) {
            // 历史数据
            return fetchCmPipelineInfo(pipelineId);
        }

        // FIXME: 当发现轮询结果为异常时，需要将异常信息返回给前端
        if (record.getPipelineStatus() == ArchivePipelineStatus.FAILED) {
            throw new RuntimeException("当前视频处理异常:" + record.getAuditReason());
        }


        if (record.getPipelineStage() >= CmArchivePipelineStage.WAIT_FOR_XCODE_META) {

            Optional<List<CoverBo>> recommendCovers = Try.of(() -> {

                MgkArchiveRecordExtra extra = JsonUtil.readValue(record.getExtra(), MgkArchiveRecordExtra.class);

                if (extra == null || CollectionUtils.isEmpty(extra.getRecommendCovers())) {
                    throw new IllegalArgumentException("推荐封面为空");
                }
                return Optional.of(extra.getRecommendCovers());
            }).getOrElse(Optional.empty());

            CoverBo selectedCover = CoverBo.builder()
                    .url(record.getCoverUrl())
                    .md5(record.getCoverMd5())
                    .source(record.getCoverOrigin())
                    .build();

            Tuple2<Boolean, String> canSaveAndMsg = Try.of(() -> {
                Assert.isTrue(Objects.equals(record.getAvid(), 0L), "对应的稿件已存在: avid=" + record.getAvid());
                return Tuple.of(true, "");
            }).getOrElseGet(t -> {
                return Tuple.of(false, t.getMessage());
            });

            return PreUploadBo.builder()
                    .cid(record.getCid().toString())
                    .cover(selectedCover)
                    .width(record.getWidth())
                    .height(record.getHeight())
                    .recommendCovers(recommendCovers.orElse(List.of(selectedCover)))
                    .canSave(canSaveAndMsg._1())
                    .cannotSaveReason(canSaveAndMsg._2())
                    .build();
        }

        return null;
    }

    @Deprecated
    public PreUploadBo fetchCmPipelineInfo(String pipelineId) {
        // 获取稿件记录
        final var mgkCmArchiveRecord = fetchLegacy(pipelineId);
        if (Objects.isNull(mgkCmArchiveRecord)) {
            return null;
        }

        final var preUploadBo = PreUploadBo.builder()
            .cid(String.valueOf(mgkCmArchiveRecord.getCid()))
            .width(mgkCmArchiveRecord.getWidth())
            .height(mgkCmArchiveRecord.getHeight())
            .build();

        // 返回稿件封面
        if (StringUtils.hasText(mgkCmArchiveRecord.getCoverUrl()) && StringUtils.hasText(mgkCmArchiveRecord.getCoverMd5())) {
            preUploadBo.setCover(CoverBo.builder()
                .url(mgkCmArchiveRecord.getCoverUrl())
                .md5(mgkCmArchiveRecord.getCoverMd5())
                .build());
        }
        return preUploadBo;
    }

    private MgkCmArchiveRecord fetchLegacy(String pipelineId) {
        // 获取流水线进度(redis)
        final var progress = pipelineService.getPipelineProgress(pipelineId);
        if (pipelineService.isPipelineErr(progress)) {
            throw new ServerException(500, Optional.ofNullable(pipelineService.getErrorMsg(pipelineId)).orElse("内部错误"));
        }

        if (!pipelineService.isPipelineDone(progress)) {
            return null;
        }

        // 获取流水线 meta map
        final var meta = pipelineService.getMeta(pipelineId);
        final var md5 = meta.get("md5");
        final var accountId = Integer.valueOf(meta.get("accountId"));
        var mid = Long.valueOf(meta.get("mid"));
        if (Objects.equals(mid, 0L)) {
            mid = cmSpaceService.fetchMid(accountId);
        }
        return mgkCmArchiveDaoService.fetchCmArchive(mid, md5);
    }

    public List<MgkCmArchiveRecord> fetchVideoInfoByAvids(List<Long> aids , List<String> md5s) {
        //都为空则不查
        if (CollectionUtils.isEmpty(aids) && CollectionUtils.isEmpty(md5s)){
            return new ArrayList<>();
        }
        List<MgkCmArchiveRecord> list = mgkCmArchiveDaoService.listByAvidOrMd5(aids,md5s);
        log.info("fetchVideoInfoByAvids avids={},md5s={} ,resultSize={}",aids,md5s,list.size());
        return list;
    }



    @Transactional(MgkDataSourceConfig.MGK_TX_MGR)
    public void delArchive(Long accountId, List<Long> cids) {

        //1、查询流水线成功或者失败的稿件，进行中的稿件不允许删除
        List<MgkCmArchiveRecord> mgkCmArchiveRecords = mgkCmArchiveDaoService.fetchCmArchiveByCids(cids);
        //2、查询老数据,stage=0,status=0,uniqueId为""
        List<MgkCmArchiveRecord> mgkOldCmArchiveRecord = mgkCmArchiveDaoService.fetchOldCmArchiveByCids(cids);
        Optional.ofNullable(mgkOldCmArchiveRecord).ifPresent(mgkCmArchiveRecords::addAll);
        //mgkCmArchiveRecords去重
        mgkCmArchiveRecords = mgkCmArchiveRecords.stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mgkCmArchiveRecords)){
            return;
        }

        //分情况，
        //1、无avid且审核未通过则删除记录，2有avid则要判断accountid是否相同，相同则调用主站接口删稿，再删推送记录，3accountid不同则删除推送记录
//        List<MgkCmArchiveRecord> rejectArchives = mgkCmArchiveRecords.stream()
//                .filter(record -> !NumberUtils.isPositive(record.getAvid()) && !NumberUtils.isPositive(record.getAuditStatus()))
//                .collect(Collectors.toList());

        //过滤出自己的稿件或推送的稿件,自己的稿件没有aid的代表流水线还没投稿，删除当前记录记录
        List<MgkCmArchiveRecord> delArchives = mgkCmArchiveRecords.stream()
                .filter(record -> record.getAccountId().equals(accountId.intValue()))
                .filter(record -> NumberUtils.isPositive(record.getAvid()))
                .collect(Collectors.toList());

        List<MgkCmArchiveRecord> delMapping = mgkCmArchiveRecords.stream()
                .filter(record -> !record.getAccountId().equals(accountId.intValue()))
                .collect(Collectors.toList());

        List<MgkCmArchiveRecord> noCreateArchives = mgkCmArchiveRecords.stream()
                .filter(record -> record.getAccountId().equals(accountId.intValue()))
                .filter(record -> !NumberUtils.isPositive(record.getAvid()))
                .collect(Collectors.toList());

//        if (!CollectionUtils.isEmpty(rejectArchives)){
//            List<Long> rejectCids = rejectArchives.stream().map(MgkCmArchiveRecord::getCid).collect(Collectors.toList());
//            log.info("delArchive rejectCids={},account_id={}", rejectCids, accountId);
//            mgkCmArchiveDaoService.delArchivesByCids(rejectCids);
//        }

        if (!CollectionUtils.isEmpty(delMapping)) {
            //删除推送记录
            List<Long> aids = delMapping.stream().map(MgkCmArchiveRecord::getAvid).collect(Collectors.toList());
            log.info("delArchive mapping avids={},account_id={}", aids, accountId);
            mgkCmArchiveAccountMappingDaoService.batchDeleteAccountId(accountId.intValue(), aids);
        }
        if (!CollectionUtils.isEmpty(noCreateArchives)){
            //删除记录
            List<Long> noCreateCids = noCreateArchives.stream().map(MgkCmArchiveRecord::getCid).collect(Collectors.toList());
            log.info("delArchive noCreateArchive={},account_id={}", noCreateCids, accountId);
            mgkCmArchiveDaoService.delArchivesByCids(noCreateCids, accountId.intValue());
        }


        if (!CollectionUtils.isEmpty(delArchives)){
            //调用主站删除接口
            for (MgkCmArchiveRecord delUgcArchive : delArchives) {
                Long avid = delUgcArchive.getAvid();
                Long mid = delUgcArchive.getMid();
                //考虑单删不阻塞后续。
                delArchiveSingle(accountId, mid, avid);
            }
        }

        this.tryNotifyArchiveDeleted(mgkCmArchiveRecords, delArchives, delMapping, noCreateArchives);

    }


    private void tryNotifyArchiveDeleted(
            List<MgkCmArchiveRecord> relatedRootArchives,

            List<MgkCmArchiveRecord> delArchives,

            List<MgkCmArchiveRecord> delMapping,

            List<MgkCmArchiveRecord> delHalfCreateArchives
    ) {

        relatedRootArchives.stream().collect(Collectors.groupingBy(MgkCmArchiveRecord::getVideoMd5))
                .forEach((md5, videos) -> {
                    Try.run(() -> {

                        if (CollectionUtils.isEmpty(videos)) {
                            return;
                        }

                        Set<String> delMd5s = Streams.concat(
                                delArchives.stream().map(a -> a.getVideoMd5()),
                                delMapping.stream().map(a -> a.getVideoMd5()),
                                delHalfCreateArchives.stream().map(a -> a.getVideoMd5())
                        ).collect(Collectors.toSet());

                        if (delMd5s.contains(md5)) {

                            aggregationViewUpdateEventPublisher.onRecordUpdated(videos.get(0), null);
                        }

                    });
                });


    }


    /**
     * 调主站删除接口不能快，qps最高为5
     * @param
     * @param
     */
    @Transactional(value = MgkDataSourceConfig.MGK_TX_MGR, propagation = Propagation.REQUIRES_NEW)
    public void delArchiveSingle(Long accountId, Long mid, Long avid) {
        //先调主站删除接口
        BiliDataApiResponse response = biliArchiveService.delArchive(mid, avid);
        if (response.isSuccess() && response.getCode() == 0) {
            log.info("delArchive bili avid={},mid={},accountId={}",avid,mid,accountId);
            //再删from_account_id的映射记录
            mgkCmArchiveDaoService.delArchivesByAvid(accountId, avid);
            mgkCmArchiveAccountMappingDaoService.batchDeleteFromAccountId(accountId.intValue(), avid);
        }

        //线程休眠200ms
        try {
            Thread.sleep(200);
        } catch (InterruptedException e) {
            log.error("delArchiveSingle sleep error", e);
        }
    }


    public List<MgkCmArchiveRecord> scan(
            Long cursorId,
            int limit,
            boolean desc,
            boolean filterHavingExtra,
            boolean filterExtraIsEmpty,
            Integer pipelineStageGte,
            Long minIdInclusive,
            Long maxIdInclusive) {

        Condition condition = MGK_CM_ARCHIVE.IS_DELETED.eq(0);

        if (cursorId != null) {

            if (desc) {
                // 倒序
                condition = condition.and(MGK_CM_ARCHIVE.ID.lt((int) cursorId.intValue()));
            } else {
                // 正序
                condition = condition.and(MGK_CM_ARCHIVE.ID.gt((int) cursorId.intValue()));
            }
        }

        if (filterHavingExtra) {
            // TODO NULL
            condition = condition.and(MGK_CM_ARCHIVE.EXTRA.ne(""))
                    .and(MGK_CM_ARCHIVE.EXTRA.isNotNull());
        }

        if (filterExtraIsEmpty) {
            condition = condition.and(MGK_CM_ARCHIVE.EXTRA.eq(""));
        }


        if (minIdInclusive != null) {
            condition = condition.and(MGK_CM_ARCHIVE.ID.ge((int) minIdInclusive.intValue()));
        }
        if (maxIdInclusive != null) {
            condition = condition.and(MGK_CM_ARCHIVE.ID.le((int) maxIdInclusive.intValue()));
        }

        if (pipelineStageGte != null) {
            condition = condition.and(MGK_CM_ARCHIVE.PIPELINE_STAGE.ge(pipelineStageGte));
        }


        List<MgkCmArchiveRecord> list = mgk.selectFrom(MGK_CM_ARCHIVE)
                .where(condition)
                .orderBy(desc ? MGK_CM_ARCHIVE.ID.desc() : MGK_CM_ARCHIVE.ID.asc())
                .limit(limit)
                .fetch();

        return list;


    }


}
