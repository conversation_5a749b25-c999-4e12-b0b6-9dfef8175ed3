/*
 * Copyright (c) 2015-2023 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.service.xxl.tempus_jobs;

import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.AdDataSourceConfig;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.tables.pojos.LogAdpOperationPo;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.Tables.LOG_ADP_OPERATION;

/**
 * 操作日志清理任务
 */
@Slf4j
@Service
public class DeprecatingOperationLogTempusJob implements BasicProcessor {
    
    public static final String ID = "DeprecatingOperationLogTempusJob";
    private int batchSize = 1000;
    private int waitMs = 1000;
    private int maxBatches = 100;
    private final DSLContext dsl;
    private int days = 10;

    public DeprecatingOperationLogTempusJob(@Qualifier(AdDataSourceConfig.AD_DSL_CONTEXT) DSLContext dsl) {
        this.dsl = dsl;
    }

    private void parse(String param, OmsLogger logger) {
        if (!StringUtils.hasText(param)) return;

        for (String exp : param.split("&")) {
            final var kvs = exp.split("=");
            Assert.isTrue(kvs.length == 2, "参数错误");

            if (!StringUtils.hasText(kvs[1])) continue;

            if (Objects.equals(kvs[0], "batchSize")) {
                batchSize = Integer.parseInt(kvs[1]);
                logger.info("设置 batchSize: {}", batchSize);
            } else if (Objects.equals(kvs[0], "waitMs")) {
                waitMs = Integer.parseInt(kvs[1]);
                logger.info("设置 waitMs: {}", waitMs);
            } else if (Objects.equals(kvs[0], "maxBatches")) {
                maxBatches = Integer.parseInt(kvs[1]);
                logger.info("设置 maxBatches: {}", maxBatches);
            } else if (Objects.equals(kvs[0], "days")) {
                days = Integer.parseInt(kvs[1]);
                logger.info("设置 days: {}", days);
            }
        }
    }

    @Override
    public ProcessResult process(TaskContext context) throws Exception {
        OmsLogger logger = context.getOmsLogger();
        String jobParams = Optional.ofNullable(context.getJobParams()).orElse("");
        
        logger.info("DeprecatingOperationLogTempusJob 开始执行, 参数: {}", jobParams);
        log.info("DeprecatingOperationLogTempusJob 开始执行, 参数: {}", jobParams);
        
        try {
            parse(jobParams, logger);
            logger.info("解析后的参数 - batchSize: {}, waitMs: {}, maxBatches: {}, days: {}", 
                       batchSize, waitMs, maxBatches, days);
            
            final var lastTs = Timestamp.valueOf(LocalDateTime.now().minusDays(days));
            logger.info("删除时间点: {}", lastTs);
            
            var lastId = 0L;
            int actualBatches = 0;
            int totalDeleted = 0;

            for (int i = 0; i < maxBatches; i++) {
                final var pos = dsl.select(LOG_ADP_OPERATION.ID, LOG_ADP_OPERATION.CTIME)
                    .from(LOG_ADP_OPERATION)
                    .where(LOG_ADP_OPERATION.ID.gt(lastId))
                    .orderBy(LOG_ADP_OPERATION.ID.asc())
                    .limit(batchSize)
                    .fetchInto(LogAdpOperationPo.class);
                if (CollectionUtils.isEmpty(pos)) break;

                final var lastPo = pos.get(pos.size() - 1);
                if (lastPo.getCtime().after(lastTs)) break;

                lastId = lastPo.getId();
                final var ids = pos.stream()
                    .map(LogAdpOperationPo::getId)
                    .collect(Collectors.toList());
                    
                int deletedCount = dsl.delete(LOG_ADP_OPERATION)
                    .where(LOG_ADP_OPERATION.ID.in(ids))
                    .execute();
                    
                totalDeleted += deletedCount;
                actualBatches++;
                
                TimeUnit.MILLISECONDS.sleep(waitMs);
                logger.info("执行第 {} 批, 删除 {} 条数据, lastId: {}", i + 1, deletedCount, lastId);
                log.info("执行第 {} 批, 删除 {} 条数据, lastId: {}", i + 1, deletedCount, lastId);
                
                if (pos.size() < batchSize) break;
            }
            
            logger.info("DeprecatingOperationLogTempusJob 执行成功, 总共执行 {} 批, 删除 {} 条数据", 
                       actualBatches, totalDeleted);
            log.info("DeprecatingOperationLogTempusJob 执行成功, 总共执行 {} 批, 删除 {} 条数据", 
                    actualBatches, totalDeleted);
            return new ProcessResult(true, "success");
        } catch (Throwable t) {
            logger.error("DeprecatingOperationLogTempusJob 执行失败", t);
            log.error("DeprecatingOperationLogTempusJob 执行失败", t);
            return new ProcessResult(false, "执行失败: " + t.getMessage());
        }
    }
}
