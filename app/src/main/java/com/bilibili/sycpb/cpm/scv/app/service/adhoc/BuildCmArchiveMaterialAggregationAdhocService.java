package com.bilibili.sycpb.cpm.scv.app.service.adhoc;

import com.bapis.ad.mgk.material.MaterialIdServiceGrpc;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.MaterialAggregationViewUpdateEventPublisher;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.MgkCmArchiveDaoService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.generated.tables.records.MgkCmArchiveRecord;
import com.bilibili.sycpb.cpm.scv.app.utils.IteratorHelper;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;
import pleiades.venus.starter.rpc.client.RPCClient;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/4/28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BuildCmArchiveMaterialAggregationAdhocService {

    @Resource
    private MgkCmArchiveDaoService mgkCmArchiveDaoService;

    @RPCClient("sycpb.cpm.mgk-portal")
    private MaterialIdServiceGrpc.MaterialIdServiceBlockingStub materialIdService;
    @Resource
    private MaterialAggregationViewUpdateEventPublisher archiveStatusUpdateEventPublisher;


    /*

     com.bilibili.sycpb.cpm.scv.app.service.adhoc.BuildCmArchiveMaterialAggregationAdhocService#scanAndBuildMaterialAggregationForAllExistedCmArchive
     */
    public void scanAndBuildMaterialAggregationForAllExistedCmArchive() {

        Iterator<List<MgkCmArchiveRecord>> iterator = IteratorHelper.buildIterator(
                (id, limit) -> {
                    return mgkCmArchiveDaoService.findByIdGtAndLimit(id, limit, null, null);
                },
                MgkCmArchiveRecord::getId,
                1000
        );

        ConcurrentHashMap<String, Boolean> processedMaterialIds = new ConcurrentHashMap<>();

        int parallel = 8;
        ExecutorService executorService = Executors.newFixedThreadPool(parallel);

        while (iterator.hasNext()) {

            Map<String, List<MgkCmArchiveRecord>> md52batch = iterator.next()
                    .stream()
                    .filter(item -> item.getIsDeleted() == 0)
                    .collect(Collectors.groupingBy(img -> img.getVideoMd5()));

            List<MgkCmArchiveRecord> batch = md52batch.entrySet().stream().map(entry -> entry.getValue().get(0))
                    .collect(Collectors.toList());

            ListUtils.partition(batch, parallel).forEach(partition -> {
                CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(partition.stream()
                        .map(po -> CompletableFuture.runAsync(() -> {
                            try {

                                String processedMaterialId = archiveStatusUpdateEventPublisher.onRecordUpdated(po,
                                        null);

                                if (processedMaterialId != null) {
                                    processedMaterialIds.put(processedMaterialId, true);
                                }
                            } catch (Throwable t) {
                                log.error("Fail to process archive,  videoMd5={}", po.getVideoMd5(), t);
                            }

                        }, executorService)).collect(Collectors.toList()).toArray(new CompletableFuture[0]));

                combinedFuture.join();

            });

        }


    }


}
