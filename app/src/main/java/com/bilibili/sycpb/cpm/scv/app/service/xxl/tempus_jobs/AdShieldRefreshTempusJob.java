package com.bilibili.sycpb.cpm.scv.app.service.xxl.tempus_jobs;

import com.alibaba.fastjson.JSONObject;
import com.bapis.ad.scv.story_new.StoryComponentNewStatus;
import com.bilibili.sycpb.cpm.scv.app.service.biz.shield.AdShieldService;
import com.bilibili.sycpb.cpm.scv.app.service.biz.shield.bos.AdShieldBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.shield.bos.AdShieldQueryBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.shield.bos.AdShieldSaveBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.story_component.bos.OperatorBo;
import com.bilibili.sycpb.cpm.scv.app.utils.Pager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdShieldRefreshTempusJob implements BasicProcessor {

    public static final String ID = "AdShieldRefreshJob";

    @Autowired
    private AdShieldService adShieldService;

    @Override
    public ProcessResult process(TaskContext context) throws Exception {
        log.info("AdShieldRefreshJob begin");
        try {
            AdShieldQueryBo queryBo = AdShieldQueryBo.builder()
                    .isLongTermShield(0)
                    .page(1)
                    .size(100)
                    .isOrderByAsc(true)
                    .startId(1).build();
            List<AdShieldBo> shieldBos = null;
            Long now = System.currentTimeMillis();
            do {
                Pager<AdShieldBo> adShieldBoPager = adShieldService.queryPageList(queryBo);
                shieldBos = adShieldBoPager.getData();
                if (CollectionUtils.isEmpty(shieldBos)) {
                    break;
                }
                List<AdShieldSaveBo> forbidBos = shieldBos.stream()
                        .filter(x -> x.getIsLongTermShield() == 0 && x.getEndTime() != null && x.getEndTime() < now && x.getStatus() != StoryComponentNewStatus.COMPONENT_STATUS_DISABLE_VALUE) // 筛选 超出屏蔽周期 的配置
                        .map(x -> {
                            return AdShieldSaveBo.builder()
                                    .id(x.getId())
                                    .status(2) // 禁用
                                    .operator(OperatorBo.builder().operatorName("system").build())
                                    .operatorName("system")
                                    .build();
                        })
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(forbidBos)) {
                    log.info("AdShieldRefreshJob forbidBos = {}", JSONObject.toJSONString(forbidBos));
                    forbidBos.forEach(x -> {
                        adShieldService.updateStatus(x);
                        try {
                            Thread.sleep(10);
                        } catch (InterruptedException e) {
                            throw new RuntimeException(e);
                        }
                    });
                }

                queryBo.setStartId(shieldBos.get(shieldBos.size() - 1).getId());
            } while (!CollectionUtils.isEmpty(shieldBos));

            log.info("AdShieldRefreshJob end");
            return new ProcessResult(true, "success");
        } catch (Throwable t) {
            log.error(ID, t);
            return new ProcessResult(false);
        }

    }
}

