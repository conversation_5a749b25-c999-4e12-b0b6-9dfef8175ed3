/*
 * Copyright (c) 2015-2022 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.service.wechat;

import com.bilibili.sycpb.cpm.scv.app.service.wechat.bos.TextCardMsgBo;
import com.bilibili.sycpb.cpm.scv.app.service.bili.hr.BiliHrService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.AdDataSourceConfig;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.tables.records.ResWechatMsgRecord;
import com.bilibili.sycpb.cpm.scv.app.service.http.common.HttpUtils;
import com.bilibili.sycpb.cpm.scv.app.service.http.hr.bos.HrAccountBo;
import com.bilibili.sycpb.cpm.scv.app.service.http_proxy.HttpProxyRequest;
import com.bilibili.sycpb.cpm.scv.app.service.http_proxy.HttpProxyService;
import com.bilibili.sycpb.cpm.scv.app.service.wechat.bos.*;
import com.bilibili.sycpb.cpm.scv.app.utils.constants.MoreServerCodes;
import com.bilibili.sycpb.cpm.scv.app.utils.constants.WechatMsgStyle;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import pleiades.component.env.Environment;
import pleiades.component.env.EnvironmentKeys;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.Tables.RES_WECHAT_MSG;

@Slf4j
@Service
public class WechatService {
    public static final String ID = "WechatService";
    public static final int WAITING = 0;
    public static final int SUCCESS = 1;
    public static final int FAILED = 2;
    private final WechatConfig.Property property;
    private final RedissonClient redissonClient;
    private final HttpProxyService httpProxyService;
    private final BiliHrService biliHrService;
    private final ExecutorService executorService;
    private final ObjectMapper objectMapper;
    private final DSLContext ad;

    public WechatService(WechatConfig.Property property,
                         RedissonClient redissonClient,
                         HttpProxyService httpProxyService,
                         BiliHrService biliHrService,
                         ObjectMapper objectMapper,
                         @Qualifier(AdDataSourceConfig.AD_DSL_CONTEXT) DSLContext ad) {
        this.property = property;
        this.redissonClient = redissonClient;
        this.httpProxyService = httpProxyService;
        this.biliHrService = biliHrService;
        this.objectMapper = objectMapper;
        this.ad = ad;
        this.executorService = Executors.newSingleThreadExecutor();
    }

    @PostConstruct
    private void run() {
        log.info("WechatService: 开始初始化...");
        try {
            // 先测试Redis连接
            testRedisConnection();

            final var lock = getLock();
            log.info("WechatService: Redis锁获取成功，启动后台任务");

            executorService.submit(() -> {
                log.info("WechatService: 后台任务线程启动");
                int consecutiveErrors = 0;
                final int maxConsecutiveErrors = 10;

                while (true) {
                    boolean lockAcquired = false;
                    try {
                        log.debug("WechatService: 尝试获取锁...");
                        lockAcquired = lock.tryLock();

                        if (lockAcquired) {
                            log.debug("WechatService: 锁获取成功，处理微信消息");
                            fetchAndSendWechatMessages();
                            consecutiveErrors = 0; // 重置错误计数
                        } else {
                            log.debug("WechatService: 未获取到锁，跳过本次处理");
                        }

                        log.debug("WechatService: 等待{}秒后继续", property.getExecutorWaitSeconds());
                        TimeUnit.SECONDS.sleep(property.getExecutorWaitSeconds());

                    } catch (InterruptedException e) {
                        log.warn("WechatService: 线程被中断，退出循环");
                        Thread.currentThread().interrupt();
                        break;
                    } catch (Throwable t) {
                        consecutiveErrors++;
                        log.error("WechatService: 执行错误 (连续错误次数: {}/{})", consecutiveErrors, maxConsecutiveErrors, t);

                        // 如果连续错误太多，增加等待时间
                        if (consecutiveErrors >= maxConsecutiveErrors) {
                            log.error("WechatService: 连续错误过多，延长等待时间到60秒");
                            try {
                                TimeUnit.SECONDS.sleep(60);
                            } catch (InterruptedException ie) {
                                Thread.currentThread().interrupt();
                                break;
                            }
                            consecutiveErrors = 0; // 重置计数
                        } else {
                            // 短暂等待后重试
                            try {
                                TimeUnit.SECONDS.sleep(5);
                            } catch (InterruptedException ie) {
                                Thread.currentThread().interrupt();
                                break;
                            }
                        }
                    } finally {
                        if (lockAcquired) {
                            try {
                                lock.unlock();
                                log.debug("WechatService: 锁释放成功");
                            } catch (Throwable unlockError) {
                                log.error("WechatService: 锁释放失败", unlockError);
                            }
                        }
                    }
                }
                log.info("WechatService: 后台任务线程退出");
            });
        } catch (Throwable t) {
            log.error("WechatService: 初始化失败", t);
        }
    }

    private void testRedisConnection() {
        try {
            log.info("WechatService: 测试Redis连接...");
            RBucket<String> testBucket = redissonClient.getBucket("scv_wechat_test_" + System.currentTimeMillis());
            testBucket.set("test", 1, TimeUnit.SECONDS);
            String result = testBucket.get();
            log.info("WechatService: Redis连接测试成功, result: {}", result);
        } catch (Throwable t) {
            log.error("WechatService: Redis连接测试失败", t);
            throw t;
        }
    }

    public void saveWechatRawMsg(String msg, List<String> users) {
        saveWechatMsg(WechatMsgStyle.RAW_MSG, genEnvMsg() + "\n" + msg, users);
    }

    @SneakyThrows
    public void saveWechatTextCardMsg(TextCardMsgBo bo) {
        saveWechatMsg(WechatMsgStyle.MSG_CARD, objectMapper.writeValueAsString(bo), bo.getUsers());
    }

    @SneakyThrows
    public void saveWechatMsg(Integer msgStyle, String msg, List<String> users) {
        if (CollectionUtils.isEmpty(users)) return;

        final var encodedMsg = Base64.getEncoder().encodeToString(msg.getBytes(StandardCharsets.UTF_8));
        final var encodedUsers = Base64.getEncoder().encodeToString(objectMapper.writeValueAsBytes(users));
        ad.newRecord(RES_WECHAT_MSG)
            .setMsg(encodedMsg)
            .setUsers(encodedUsers)
            .setMsgStyle(msgStyle)
            .store();
    }

    private String genEnvMsg() {
        return "环境: " + Environment.ofNullable(EnvironmentKeys.DEPLOY_ENV).orElse("未知");
    }

    @SneakyThrows
    private void fetchAndSendWechatMessages() {
        final var msgRecords = ad.fetch(RES_WECHAT_MSG, RES_WECHAT_MSG.STATUS.eq(WAITING));
        if (CollectionUtils.isEmpty(msgRecords)) return;

        for (ResWechatMsgRecord msgRecord : msgRecords) {
            final var retry = msgRecord.getRetry()-1;
            msgRecord.setRetry(retry);
            try {
                final var users = objectMapper.readValue(Base64.getDecoder().decode(msgRecord.getUsers()), new TypeReference<List<String>>() {
                });
                final var msg = new String(Base64.getDecoder().decode(msgRecord.getMsg()), StandardCharsets.UTF_8);
                if (WechatMsgStyle.isRawMsg(msgRecord.getMsgStyle())) {
                    sendTextMsg(users, msg);
                } else if (WechatMsgStyle.isMsgCard(msgRecord.getMsgStyle())) {
                    final var textCard = objectMapper.readValue(msg, TextCardMsgBo.class);
                    textCard.setUsers(users);
                    textCard.setGray(genEnvMsg());
                    sendTextCardMsg(textCard);
                } else {
                    msgRecord.setStatus(FAILED);
                }
                msgRecord.setStatus(SUCCESS);
                // 调用太快会被限流
                TimeUnit.MILLISECONDS.sleep(500);
            } catch (Throwable t) {
                if (retry == 0) {
                    msgRecord.setStatus(FAILED);
                }
                log.error(ID, t);
            } finally {
                msgRecord.store();
            }
        }
    }

    private RLock getLock() {
        return redissonClient.getLock(property.getExecutorLockKey());
    }

    private RBucket<String> bucket() {
        return redissonClient.getBucket("scv_wechat_token");
    }

    private String token() {
        final RBucket<String> bucket = bucket();
        if (bucket.isExists()) return bucket.get();

        return refreshToken(bucket);
    }

    @SneakyThrows
    private String refreshToken(RBucket<String> bucket) {
        final var httpUrl = HttpUtils.parse(property.getTokenApi())
            .newBuilder()
            .addQueryParameter("corpid", property.getCorpId())
            .addQueryParameter("corpsecret", property.getSecret())
            .build();
        final var proxyRequest = HttpProxyRequest.newInstance("get", httpUrl.toString());
        final var response = httpProxyService.clientCall(proxyRequest, WechatAccessTokenResponse.class);
        handleWechatErr("获取企业微信token失败", response);
        final var token = response.getAccessToken();
        bucket.set(token);
        bucket.expire(response.getExpiresIn(), TimeUnit.SECONDS);
        return token;
    }

    public String createChatGroup(WechatCreateChatGroupRequestBody requestBody) {
        final var httpUrl = HttpUtils.parse(property.getCreateGroupApi())
            .newBuilder()
            .addQueryParameter("access_token", token())
            .build();
        final var proxyRequest = HttpProxyRequest.newInstance("post", httpUrl.toString())
            .setProxyBody(requestBody);
        final var response = httpProxyService.clientCall(proxyRequest, WechatCreateChatGroupResponse.class);
        handleWechatErr("创建企业微信群失败", response);
        return response.getChatid();
    }

    public String sendTextCardMsg(TextCardMsgBo bo) {
        final var httpUrl = HttpUtils.parse(property.getAppMsgApi())
            .newBuilder()
            .addQueryParameter("access_token", token())
            .build();
        final var sb = new StringBuilder();
        if (StringUtils.hasText(bo.getGray())) {
            sb.append("<div class=\"gray\">")
                .append(bo.getGray())
                .append("</div>");
        }
        if (StringUtils.hasText(bo.getHighlight())) {
            sb.append("<div class=\"highlight\">")
                .append(bo.getHighlight())
                .append("</div>");
        }
        if (StringUtils.hasText(bo.getNormal())) {
            sb.append('\n')
                .append(bo.getNormal());
        }
        final var textCard = WechatTextCardMsgRequestBody.TextCard.builder()
            .title(bo.getTitle())
            .description(sb.toString())
            .url(bo.getUrl())
            .build();
        var users = bo.getUsers();
        if (CollectionUtils.isEmpty(users)) {
            users = property.getAdmins();
        }
        final var requestBody = WechatTextCardMsgRequestBody.builder()
            .touser(nickNames2WorkCodeStr(users))
            .textcard(textCard)
            .msgtype("textcard")
            .agentid(property.getAgentId())
            .build();
        final var proxyRequest = HttpProxyRequest.newInstance("post", httpUrl.toString())
            .setProxyBody(requestBody);
        final var response = httpProxyService.clientCall(proxyRequest, WechatCreateChatGroupResponse.class);
        handleWechatErr("发送企业微信模板消息失败", response);
        return response.getChatid();
    }

    private String nickNames2WorkCodeStr(Collection<String> nickNames) {
        return nickNames.stream()
            .map(biliHrService::getHrAccount)
            .filter(Objects::nonNull)
            .map(HrAccountBo::getWorkCode)
            .collect(Collectors.joining("|"));
    }

    private void sendTextMsg(List<String> users, String content) {
        if (CollectionUtils.isEmpty(users)) return;

        final var httpUrl = HttpUtils.parse(property.getAppMsgApi())
            .newBuilder()
            .addQueryParameter("access_token", token())
            .build();
        final var requestBody = WechatTextMsgRequestBody.builder()
            .text(WechatTextMsgRequestBody.Text.builder()
                .content(content)
                .build())
            .touser(nickNames2WorkCodeStr(users))
            .msgtype("text")
            .agentid(property.getAgentId())
            .build();
        final var proxyRequest = HttpProxyRequest.newInstance("post", httpUrl.toString())
            .setProxyBody(requestBody);
        final var response = httpProxyService.clientCall(proxyRequest, WechatCreateChatGroupResponse.class);
        handleWechatErr("发送企业微信消息失败", response);
    }

    private <T extends HasWechatErrCodeAndErrMsg> void handleWechatErr(String prefix, T t) {
        if (Objects.equals(t.getErrcode(), 0)) return;

        throw MoreServerCodes.generalException(MessageFormat.format("{0}: err_code={1}, err_msg={2}", prefix, t.getErrcode(), t.getErrmsg()));
    }
}
