package com.bilibili.sycpb.cpm.scv.app.service.databus.v1.watermark_erase;

import com.bilibili.databus.base.Message;
import com.bilibili.databus.config.DataBusConfig;
import com.bilibili.databus.core.DataBusClient;
import com.bilibili.databus.core.DataBusImpl;
import com.bilibili.discovery.DiscoveryClient;
import com.bilibili.sycpb.cpm.scv.app.utils.http.EnvUtils;
import io.opentelemetry.api.OpenTelemetry;
import io.vavr.control.Try;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Consumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pleiades.component.env.Environment;
import pleiades.component.env.EnvironmentKeys;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/5/7
 */
public interface DatabusConfigSupport {

    Logger log = LoggerFactory.getLogger(DatabusConfigSupport.class);

    default DataBusClient createDatabusPubV3(
            String topic, String group, String appId, String zone, String token, DiscoveryClient discoveryClient,
            OpenTelemetry discoveryNoopOpenTelemetry
    ) {

        DataBusConfig databusV3Config = new DataBusConfig(
                EnvUtils.getDatabusEnv(Environment.of(EnvironmentKeys.DEPLOY_ENV).get()),
                EnvUtils.getDatabusZone(zone),
                topic, group,
                appId,
                EnvUtils.getDatabusToken(token)
        );

        return new DataBusImpl(databusV3Config, discoveryClient, discoveryNoopOpenTelemetry);
    }


    default List<DataBusClient> createDatabusSubV3AndStartSub(
            String topic, String group, String appId, String zone, String token, Integer consumers,
            Consumer<String> consumer, DiscoveryClient discoveryClient, OpenTelemetry discoveryNoopOpenTelemetry
    ) {

        DataBusConfig databusV3Config = new DataBusConfig(
                EnvUtils.getDatabusEnv(Environment.of(EnvironmentKeys.DEPLOY_ENV).get()),
                zone,
                topic,
                group,
                appId,
                token
        );
        log.info("Start to create databus v3 client: {}", databusV3Config);

        ExecutorService executor = Executors.newFixedThreadPool(consumers);

        List<DataBusClient> subClients = new ArrayList<>();

        for (int i = 0; i < consumers; i++) {

            executor.submit(() -> {

                DataBusClient subClient = new DataBusImpl(databusV3Config, discoveryClient,
                        discoveryNoopOpenTelemetry);

                subClients.add(subClient);

                while (true) {
                    Try.run(() -> {

                        Message msg = subClient.sub();

                        if (msg == null) {
                            return;
                        }

                        Try.run(() -> {
                            consumer.accept(new String(msg.getValue(), StandardCharsets.UTF_8));

                        }).onFailure(t -> {
                            log.error("Fail to handle databus message: {}, topic={}", msg, topic, t);
                        });

                        subClient.ack(msg);

                    }).onFailure(t -> {
                        log.error("Receive databus message error: {}, topic={}", t.getMessage(), topic, t);
                    });

                }

            });
        }

        return subClients;


    }
}
