/*
 * Copyright (c) 2015-2024 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive;

import com.bapis.ad.mgk.material.MaterialIdReferencePage;
import com.bapis.ad.mgk.material.MaterialIdRegisterResp;
import com.bapis.ad.mgk.material.MaterialIdRegistry;
import com.bapis.ad.mgk.material.MaterialIdResp;
import com.bapis.ad.mgk.material.MaterialIdServiceGrpc;
import com.bapis.ad.mgk.material.MaterialUkReq;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.MaterialAggregationViewUpdateEventPublisher;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.model.ArchiveModelConvertor;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.CmArchiveListConditionV3Bo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.CmArchiveV3Bo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.MgkCmArchiveBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.VideoBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.config.MaterialIdConfigure;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.MgkCmArchiveDaoService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.MgkCmVideoDaoService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.generated.tables.records.MgkCmArchiveRecord;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.generated.tables.records.MgkCmVideoRecord;
import com.bilibili.sycpb.cpm.scv.app.utils.IteratorHelper;
import com.google.common.collect.Lists;
import io.vavr.control.Try;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/5/21
 */
@Slf4j
@Service
public class ArchiveServiceMaterialDelegate {
    @Resource
    private MaterialIdConfigure materialIdConfigure;
    @RPCClient("sycpb.cpm.mgk-portal")
    private MaterialIdServiceGrpc.MaterialIdServiceBlockingStub materialIdService;
    @Resource
    private MgkCmArchiveDaoService mgkCmArchiveDaoService;
    @Resource
    private MgkCmVideoDaoService mgkCmVideoDaoService;
    @Resource
    private MaterialAggregationViewUpdateEventPublisher materialAggregationViewUpdateEventPublisher;

    private final ExecutorService registerExecutor = Executors.newFixedThreadPool(10);


    public void scanAndRegisterAllExistedVideoArchivesAsync(
            @Nullable LocalDateTime startTime, @Nullable Integer accountId) {

        registerExecutor.submit(() -> {
            this.scanAndRegisterAllExistedVideoArchives(startTime, accountId);
        });
    }

    public void scanAndRegisterAllExistedVideoArchives(
            @Nullable LocalDateTime startTime, @Nullable Integer accountId) {

        Iterator<List<MgkCmArchiveRecord>> iterator = IteratorHelper.buildIterator(
                (id, limit) -> {
                    return mgkCmArchiveDaoService.findByIdGtAndLimit(id, limit, startTime, accountId);
                },
                MgkCmArchiveRecord::getId,
                1000
        );

        long startTs = System.currentTimeMillis();

        AtomicInteger total = new AtomicInteger(0);

        while (iterator.hasNext()) {
            List<MgkCmArchiveRecord> records = iterator.next();

            Map<String, String> md5ToFilename = this.findVideoMd5ToFilename(records);
            records.forEach(record -> {
                if (StringUtils.isNotBlank(record.getVideoMd5())) {
                    onArchiveCreated(record, md5ToFilename.get(record.getVideoMd5()));
                }
            });
            log.info("Complete iterate round, current process cnt={} ", total.addAndGet(records.size()));
        }

        log.info("Finish scanAndRegisterAllExistedVideoArchives cost={} millis, total={}",
                (System.currentTimeMillis() - startTs), total.get());


    }


    @EventListener(ArchiveUpdateEvent.class)
    public void onArchiveUpdated(ArchiveUpdateEvent event) {
        log.info("Archive updated, record={}", event.getRecord());

        if (event.getFilename() == null) {

            event.setFilename(this.findVideoMd5ToFilename(Lists.newArrayList(event.getRecord()))
                    .get(event.getRecord().getVideoMd5()));

        }

        this.onArchiveCreated(event.getRecord(), event.getFilename());
    }


    public void onArchiveCreated(MgkCmArchiveRecord record, String fileName) {

        String materialId = Try.of(() -> {
                    MaterialIdRegisterResp r = materialIdService.register(
                            ArchiveModelConvertor.instance.record2MaterialRegisterReq(record, fileName));
                    return r.getMaterialId();
                }).onFailure(t -> {
                    log.error("Fail to register material, record={}", record, t);
                })
                .onSuccess(r -> {
                    log.info("Success to register material, record={}", record.getVideoMd5());
                }).getOrElseGet(t -> {
                    return Try.of(() -> {
                        MaterialIdResp r = materialIdService.findByTypeAndUks(
                                MaterialUkReq.newBuilder()
                                        .setMaterialIdType("video")
                                        .addAllMaterialUks(Lists.newArrayList(record.getVideoMd5()))
                                        .build());

                        if (r == null || r.getMaterialIdsMap().isEmpty()) {
                            return null;
                        }
                        return r.getMaterialIdsMap().get(record.getVideoMd5()).getMaterialId();

                    }).getOrNull();

                });

        if (materialId != null) {
            Try.run(() -> {

                materialAggregationViewUpdateEventPublisher.onRecordUpdated(record, materialId);
            }).onFailure(t -> {
                log.error("Fail to trigger material aggregation update event, record={}", record, t);
            }).onSuccess(r -> {
                log.info("Success to trigger material aggregation update event, record={}", record.getVideoMd5());
            });
        }

    }

    /**
     * md5-> filename
     */
    private Map<String, String> findVideoMd5ToFilename(List<MgkCmArchiveRecord> records) {

        return Try.of(() -> {
            // 提取稿件 md5
            List<String> rawMd5s = records.stream()
                    .map(MgkCmArchiveRecord::getVideoMd5)
                    .distinct()
                    .collect(Collectors.toList());
            // 获取商业视频素材

            List<MgkCmVideoRecord> mgkCmVideoRecords = mgkCmVideoDaoService.fetchByMd5s(rawMd5s);

            Map<String, String> md5RawNameMap = mgkCmVideoRecords.stream()
                    .collect(Collectors.toMap(MgkCmVideoRecord::getMd5, MgkCmVideoRecord::getRawName, (v1, v2) -> v2));

            return md5RawNameMap;
        }).onFailure(t -> {
            log.error("Fail to findVideoMd5ToFilename, md5={}", records.stream()
                    .map(MgkCmArchiveRecord::getVideoMd5)
                    .distinct()
                    .collect(Collectors.toList()), t);
        }).getOrElse(new HashMap<>());


    }





    public List<MgkCmArchiveBo> filterByMaterialSearchWord(List<MgkCmArchiveBo> records, CmArchiveListConditionV3Bo conditionBo){

        if (!conditionBo.searchByMaterialIdCenter()) {
            return records;
        }

        // 分页直接依赖archive因为本身也是做内存分页的，没有太大的性能损失
        MaterialIdReferencePage materialReferences = materialIdService
            .searchReference(conditionBo.toMaterialReferenceSearchReq(materialIdConfigure.getReferenceSearchMaxSize(),
                records.stream().map(record -> record.getAccountId()).distinct().collect(Collectors.toList())));

        if (materialReferences.getTotalCount() > materialIdConfigure.getReferenceSearchMaxSize()) {
            log.error("Notice: material-reference search result count > max_size, some result was missing");
        }


        Set<Integer> primaryIds = materialReferences.getDataList().stream()
            .map(reference -> {
                return ArchiveModelConvertor.instance.extractPrimaryKeyFromReferenceUk(reference.getReferenceUk());
            })
            .filter(opt -> opt.isPresent())
            .map(opt -> opt.orElse(null))
            .collect(Collectors.toSet());

        if(CollectionUtils.isEmpty(primaryIds)) {
            return new ArrayList<>();
        }

        return records.stream().filter(record -> primaryIds.contains(record.getId())).collect(Collectors.toList());


    }


    public void fillingMaterialIdOfArchive(List<CmArchiveV3Bo> data){
        Try.run(() -> this.fillingMaterialIds(Optional.ofNullable(data)
                .map(video -> video.stream()
                    .map(v -> v.getVideo())
                    .collect(Collectors.toList()))
                .orElse(new ArrayList<>())))
            .onFailure(t -> {
                log.error("Fail to fill material id", t);
            });

    }


    private void fillingMaterialIds(List<VideoBo> videos){

        List<String> absentUks = videos.stream()
                .filter(video -> StringUtils.isEmpty(video.getMaterialId()))
                .map(VideoBo::getMd5)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(absentUks)) {
            return;
        }

        Map<String, MaterialIdRegistry> uk2materialIds = materialIdService.findByTypeAndUks(MaterialUkReq.newBuilder()
                        .setMaterialIdType("video")
                        .addAllMaterialUks(absentUks)
                        .build())
                .getMaterialIdsMap();

        videos.forEach(video -> {
            Optional<String> opt = Optional.ofNullable(uk2materialIds.get(video.getMd5()))
                .map(MaterialIdRegistry::getMaterialId);
            video.setMaterialId(opt.orElse(""));

        });

    }




    /**
     * 素材添加或者标题、url更新事件
     */
    @Setter
    @Getter
    @Accessors(chain = true)
    public static class ArchiveUpdateEvent extends ApplicationEvent {


        private MgkCmArchiveRecord record;

        @Nullable
        private String filename;

        public ArchiveUpdateEvent(Object source) {
            super(source);
        }


    }


}
