/*
 * Copyright (c) 2015-2023 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm;

import com.bapis.ad.adp.archive.*;
import com.bilibili.sycpb.cpm.scv.app.executor.v2.ExecutorConfigV2;
import com.bilibili.sycpb.cpm.scv.app.executor.v2.ExecutorResources;
import com.bilibili.sycpb.cpm.scv.app.service.bili.archive.BiliArchiveService;
import com.bilibili.sycpb.cpm.scv.app.service.bili.bvcflow.BiliBvcFlowService;
import com.bilibili.sycpb.cpm.scv.app.service.bili.upos.BiliUposService;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.ArchivePipelineException;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.ArchivePipelineNotifyService;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.ArchivePipelineServerCodes;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.ArchivePipelineStatus;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.MaterialAggregationViewUpdateEventPublisher;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.adx.AdxArchivePipelineStage;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm.WatermarkEraseConfiguration.AlgoVideoCoverConfig;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm.bos.CmArchiveContextBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm.bos.CmArchiveExtraBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm.bos.CmArchiveStage0CompleteEvent;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.ArchiveServiceMaterialDelegate.ArchiveUpdateEvent;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.CmArchiveCommentService;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.CmArchiveService;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.CmSpaceService;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.CoverBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.local_stroage.LocalStorageBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.local_stroage.LocalStorageService;
import com.bilibili.sycpb.cpm.scv.app.service.boss.BossService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.account.AccountService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.LaunchArchiveService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.MgkAdxSpaceDaoService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.MgkCmArchiveDaoService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.MgkCmVideoDaoService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.generated.tables.records.MgkCmArchiveRecord;
import com.bilibili.sycpb.cpm.scv.app.service.rpc.BiliArchiveExtraService;
import com.bilibili.sycpb.cpm.scv.app.service.rpc.ImRpcService;
import com.bilibili.sycpb.cpm.scv.app.service.rpc.PassportRpcService;
import com.bilibili.sycpb.cpm.scv.app.utils.FunctionUtils;
import com.bilibili.sycpb.cpm.scv.app.utils.JsonUtil;
import com.bilibili.sycpb.cpm.scv.app.utils.NumberUtils;
import com.bilibili.sycpb.cpm.scv.app.utils.VideoTools;
import com.bilibili.sycpb.cpm.scv.app.utils.VideoTools.FetchCoverResult;
import com.bilibili.sycpb.cpm.scv.app.utils.VideoTools.VideoDownloadResult;
import com.bilibili.sycpb.cpm.scv.app.utils.constants.ArchiveMode;
import com.bilibili.sycpb.cpm.scv.app.utils.constants.ArchivePipelineType;
import com.bilibili.sycpb.cpm.scv.app.utils.constants.AuthStatus;
import com.bilibili.sycpb.cpm.scv.app.utils.constants.MoreServerCodes;
import com.bilibili.sycpb.cpm.scv.app.utils.constants.SizeType;
import com.bilibili.sycpb.cpm.scv.app.utils.constants.UpFrom;
import com.bilibili.sycpb.cpm.scv.app.utils.constants.XcodeStatus;
import com.bilibili.sycpb.cpm.scv.app.utils.enums.ArchiveCoverOriginEnum;
import io.vavr.control.Try;
import java.text.MessageFormat;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;
import pleiades.component.ecode.ServerCode;
import pleiades.venus.starter.rpc.client.RPCClient;

@Slf4j
@Service("CmArchivePipelineV2Service")
@RequiredArgsConstructor
public class CmArchivePipelineService {
    private static final String ID = "CmArchivePipelineService";

    private final BossService bossService;
    private final LocalStorageService localStorageService;
    private final MgkCmArchiveDaoService mgkCmArchiveDaoService;
    private final MgkCmVideoDaoService mgkCmVideoDaoService;
    private final MgkAdxSpaceDaoService mgkAdxSpaceDaoService;
    private final BiliUposService biliUposService;
    private final BiliBvcFlowService biliBvcFlowService;
    private final BiliArchiveService biliArchiveService;
    private final CmSpaceService cmSpaceService;
    private final PassportRpcService passportRpcService;
    private final LaunchArchiveService launchArchiveService;
    private final ImRpcService imRpcService;
    private final AccountService accountService;
    private final ArchivePipelineNotifyService archivePipelineNotifyService;
    private final RedissonClient redissonClient;
    private final BiliArchiveExtraService biliArchiveExtraService;
    private final ApplicationEventPublisher eventPublisher;
    private final AlgoVideoCoverLocalScheduleService algoVideoCoverLocalScheduleService;
    private final AlgoVideoCoverConfig algoVideoCoverConfig;
    private final CmArchiveCommentService cmArchiveCommentService;
    private final VideoTools videoTools;
    private final MaterialAggregationViewUpdateEventPublisher aggregationViewUpdateEventPublisher;
    private final CmArchiveValidateService cmArchiveValidateService;

    @Value("${scv.archive.upload.max-size-kb:614400}")
    private Integer maxUploadArchiveSize;

    @Value("${scv.archive.upload.max-size-kb:1048576}")
    private Integer maxUploadArchiveSizeForOgv;

    @Value("${scv.archive.bvc-xcode-preview-poll-max-attempts:120}")
    private Integer bvcXCodePreviewPollMaxAttempts;

    @Value("${scv.archive.bvc-xcode-preview-poll-attempt-interval-seconds:5}")
    private Integer bvcXcodePreviewPollAttemptIntervalSeconds;



    @Resource(name = ExecutorConfigV2.EXECUTOR_MAP)
    private Map<String, ThreadPoolExecutor> executorMap;


    public static void validateVideoFileName(String name) {
        if (StringUtils.hasText(name)) {
            Assert.isTrue(name.length() < 151, "视频文件名过长");
        }
    }

    public static void validateTitle(String title) {
        Assert.isTrue(StringUtils.hasText(title), "标题不能为空");
        Assert.isTrue(!title.startsWith(" ") && !title.endsWith(" "), "标题不能以空格开始/结束");
    }

    public static void validateTags(List<String> tags) {
        Assert.isTrue(!CollectionUtils.isEmpty(tags) && tags.size() <= 10, "视频包含的标签数至少1个至多10个");
        var len = 0;
        for (String tag : tags) {
            Assert.isTrue(!tag.contains(","), "标签不能包含逗号");
            len += tag.length() + 1;
        }
        Assert.isTrue(len < 256, "视频标签的内容过长");
    }


    public CmArchiveContextBo genPostAuthContext(MgkCmArchiveRecord record) {
        return CmArchiveContextBo.builder()
                .record(record)
                .signature(genSignature(record))
                .pipeline(genPipeline(true, 1))
                .build();
    }


    public CmArchiveContextBo genPostTwoStageSubmitContext(MgkCmArchiveRecord record) {
        final boolean isAuthFree;
        if (ArchiveMode.isUserSpace(record.getArchiveMode())) {
            isAuthFree = launchArchiveService.isAuthFree(record.getMid(), record.getAccountId());
        } else {
            isAuthFree = true;
        }
        return CmArchiveContextBo.builder()
                .record(record)
                .signature(genSignature(record))
                .pipeline(genPipeline(isAuthFree, 1))
                .build();
    }

    @SneakyThrows
    public String triggerCmArchivePipelineAsync(CmArchiveContextBo ctx) {
        final var resourceKey = ctx.isMapi() ? ExecutorResources.MAPI_ARCHIVE_PIPELINE_KEY : (ArchiveMode.isBrandOgv(ctx.getArchiveMode()) ? ExecutorResources.BRAND_OGV_ARCHIVE_PIPELINE_KEY : ExecutorResources.CM_ARCHIVE_PIPELINE_KEY);
        final var executor = executorMap.get(resourceKey);
        if (Objects.isNull(executor)) throw new RuntimeException("未配置对应的资源");
        if (ctx.isMapi() && NumberUtils.isPositive(ctx.getMid())) {
            cmArchiveValidateService.checkMidAccountBiliAuthRelation(ctx.getAccountId(), ctx.getMid());
        }
        // 构建context，包括编排构建流水线pipeline
        preprocess(ctx);
        mgkCmArchiveDaoService.createIfNotExists(
                ctx.getArchiveMode(), ctx.getAccountId(),
                ctx.getMid(), ctx.getVideoUrl(),
                ctx.getVideoMd5(), ctx.getUniqueId(),
                ctx.getSource(), ctx.getTitle(),
                ctx.getBiliCategoryOne(), ctx.getBiliCategoryTwo(),
                ctx.getTags(), ctx.getIpv6(),
                ArchivePipelineType.NORMAL, ctx.getIsWatermarkErase());
        final var lock = getLock(ctx.getUniqueId());
        final var acquired = lock.tryLock(0, 30, TimeUnit.MINUTES);
        // 锁的粒度是从尝试入队到执行完毕, 也就是说对于一个投稿记录, 不会有两个线程同时修改
        if (acquired) {
            try {

                executor.execute(() -> {
                    try {
                        // 开始执行之前才设置这个数据, 避免过早设置在排队过程中数据过期, 导致流程出错
                        ctx.setRecord(mgkCmArchiveDaoService.fetchExistingRecord(ctx.getUniqueId()));
                        triggerCmArchivePipeline(ctx);
                    } finally {
                        // 解锁时机1: 入队并执行完毕(不论成功失败)
                        lock.unlock();
                    }
                });

                // append parallel task
                if (algoVideoCoverConfig.getAlgoCoverEnabled() &&
                        Optional.ofNullable(ctx.getIsUsingAlgoCover())
                                // 目前缺省的就是mapi，这里也包括进去
                                .orElse(algoVideoCoverConfig.getTriggerAlgoCoverIncludingMapi())) {

                    // 推荐封面与正常的任务同时起跑
                    ctx.setAlgoCoverFuture(algoVideoCoverLocalScheduleService.schedule(
                            ctx.getVideoDownloadUrl(),
                            ctx.getAccountId()));
                }

            } catch (RejectedExecutionException e) {
                // 解锁时机2: 没有拿到线程池资源
                lock.unlock();
                log.error("{}: failed to allocate resources from thread pool, signature -> {}", ID, ctx.getSignature());
                throw new RuntimeException("当前无可用资源, 请稍后重试");
            }
            // 如果有其他意外退出的情况, 最多锁30分钟
        } else {
            // 没拿到锁, 说明有其他pipeline在排队/执行
            log.info("{}: pipeline is enqueued, request is ignored, signature={}", ID, ctx.getSignature());
        }
        return ctx.getUniqueId();
    }



    private void preprocess(CmArchiveContextBo ctx) {
        if (Objects.isNull(ctx.getStage())) {
            validateTitle(ctx.getTitle());
        }
        validateVideoFileName(ctx.getVideoFileName());
        final MgkCmArchiveRecord existingRecord;
        int maxSizeKb = maxUploadArchiveSize;
        boolean downloadFromMd5 = true;
        final byte[] ipv6;
        final boolean isAuthFree;
        if (ArchiveMode.isUserSpace(ctx.getArchiveMode())) {
            Assert.isTrue(NumberUtils.isPositive(ctx.getAccountId()), "账号不能为空");
            Assert.isTrue(StringUtils.hasText(ctx.getVideoMd5()), "视频md5不能为空");
            Assert.isTrue(NumberUtils.isPositive(ctx.getMid()), "mid不能为空");
            if (Objects.isNull(ctx.getStage())) {
                Assert.isTrue(NumberUtils.isPositive(ctx.getBiliCategoryOne()), "主站一级分区不能为空");
                Assert.isTrue(NumberUtils.isPositive(ctx.getBiliCategoryTwo()), "主站二级分区不能为空");
                validateTags(ctx.getTags());
            }
            ctx.setUniqueId(DigestUtils.md5Hex(ctx.getArchiveMode() + "-" + ctx.getVideoMd5()));
            ipv6 = passportRpcService.fetchActiveIp(ctx.getMid());
            existingRecord = mgkCmArchiveDaoService.fetchUserSpaceExistingRecord(ctx.getVideoMd5());
            final var existingCmSpaceRecord = mgkCmArchiveDaoService.fetchCmSpaceExistingRecord(ctx.getAccountId(),
                    ctx.getVideoMd5(), ArchivePipelineType.NORMAL);

            Assert.isNull(existingCmSpaceRecord, "同一个视频已经存在子账号投稿流程, 无法在真实账号继续投稿");
            isAuthFree = launchArchiveService.isAuthFree(ctx.getMid(), ctx.getAccountId());
        } else {
            ctx.setBiliCategoryOne(160);
            ctx.setBiliCategoryTwo(21);
            ctx.setTags(List.of("广告"));
            ipv6 = null;
            isAuthFree = true;
            if (ArchiveMode.isCmSpace(ctx.getArchiveMode())) {
                Assert.isTrue(NumberUtils.isPositive(ctx.getAccountId()), "账号不能为空");
                Assert.isTrue(StringUtils.hasText(ctx.getVideoMd5()), "视频md5不能为空");
                // 每个账号对应一个固定的mid
                ctx.setMid(cmSpaceService.fetchMid(ctx.getAccountId()));
                ctx.setUniqueId(
                        DigestUtils.md5Hex(ctx.getArchiveMode() + "-" + ctx.getAccountId() + "-" + ctx.getVideoMd5()));
                existingRecord = mgkCmArchiveDaoService.fetchCmSpaceExistingRecord(ctx.getAccountId(),
                        ctx.getVideoMd5(), ArchivePipelineType.NORMAL);
            } else if (ArchiveMode.isBrandOgv(ctx.getArchiveMode())) {
                Assert.isTrue(StringUtils.hasText(ctx.getVideoUrl()), "视频链接不能为空");
                Assert.isTrue(NumberUtils.isPositive(ctx.getSource()), "来源不能为空");
                Assert.isTrue(StringUtils.hasText(ctx.getUniqueId()), "唯一id不能为空");
                // 每个source对应一个mid池, 随机从mid池里取一个
                final var mids = mgkAdxSpaceDaoService.fetchMidsBySource(ctx.getSource());
                Assert.isTrue(!CollectionUtils.isEmpty(mids), "source=" + ctx.getSource() + "对应的mid池不能为空");
                final var nextIdx = new Random(System.currentTimeMillis()).nextInt(mids.size());
                ctx.setMid(mids.get(nextIdx));
                existingRecord = mgkCmArchiveDaoService.fetchBrandOgvExistingRecord(ctx.getUniqueId());
                maxSizeKb = maxUploadArchiveSizeForOgv;
                downloadFromMd5 = false;
            } else {
                throw new IllegalArgumentException("无法识别archive_mode=" + ctx.getArchiveMode());
            }
        }
        if (!StringUtils.hasText(ctx.getVideoUrl())) {
            ctx.setVideoUrl(bossService.genDownloadUrl(CmArchiveService.CM_ARCHIVE_BOSS_BUCKET_KEY, ctx.getVideoMd5()));
        }

        ctx.setIpv6(ipv6);
        ctx.setMaxSizeKb(maxSizeKb);

        // 生成流水线
        ctx.setPipeline(genPipeline(isAuthFree, ctx.getStage()));
        ctx.setDownloadFromMd5(Optional.ofNullable(ctx.getSpecificIsDownloadFromMd5()).orElse(downloadFromMd5));

        if (ctx.isDownloadFromMd5()) {
            ctx.setVideoDownloadUrl(videoTools.videoMd5ToDownloadUrl(ctx.getVideoMd5()));
        } else {
            ctx.setVideoDownloadUrl(ctx.getVideoUrl());
        }

        final String signature = genSignature(ctx);
        ctx.setSignature(signature);
        // false
        if (Objects.nonNull(existingRecord) && !StringUtils.hasText(existingRecord.getUniqueId())) {
            // 历史数据没有unique_id, 这里补上
            if (NumberUtils.isPositive(existingRecord.getAvid())) {
                existingRecord.setPipelineStage(CmArchivePipelineStage.ARCHIVE_POST_PROCESS);
            }
            existingRecord.setUniqueId(ctx.getUniqueId())
                    .store();
        }
    }


    public void triggerCmArchivePipeline(CmArchiveContextBo ctx) {
        final var record = ctx.getRecord();
        try {
            for (Consumer<CmArchiveContextBo> consumer : ctx.getPipeline()) {
                consumer.accept(ctx);
            }
            // 这个状态看上去有问题， stage0完成就是SUCCEEDED stage1完成还是是SUCCEEDED，
            // 但是如果stage0完成后stage1失败了那么会从SUCCEEDED变成失败
            record.setPipelineStatus(ArchivePipelineStatus.SUCCEEDED)
                    .store();

            if (Objects.equals(ctx.getStage(), 0)) {

                eventPublisher.publishEvent(new CmArchiveStage0CompleteEvent(this)
                        .setArchiveRecord(ctx.getRecord()));
            }

        } catch (ArchivePipelineException e) {
            if (Objects.isNull(e.getErr())) {
                log.error("{}: sig={}, code={}, msg={}", ID, ctx.getSignature(), e.getCode(), e.getMsg());
            } else {
                log.error(MessageFormat.format("{0}: sig={1}, code={2}, msg={3}", ID, ctx.getSignature(), e.getCode(), e.getMsg()), e.getErr());
            }
            record.setPipelineStatus(ArchivePipelineStatus.FAILED)
                    .setAuditStatus(e.getCode())
                    .setAuditReason(e.getMsg())
                    .store();
            final var msgBo = CmArchiveConverter.MAPPER.toMsgBo(e.getCode(), e.getMessage(), record);
            archivePipelineNotifyService.notifyTermination(msgBo);
        } catch (Throwable t) {
            log.error(MessageFormat.format("{0}: untouchable", ID), t);
            record.setPipelineStatus(ArchivePipelineStatus.FAILED)
                    .setAuditStatus(-500)
                    .setAuditReason(t.getMessage())
                    .store();
            final var msgBo = CmArchiveConverter.MAPPER.toMsgBo(MoreServerCodes.UNKNOWN.getCode(), MoreServerCodes.UNKNOWN.getMessage(), record);
            archivePipelineNotifyService.notifyTermination(msgBo);
        } finally {
            if (Objects.nonNull(ctx.getLocalStorageBo())) {
                localStorageService.cleanUp(false, ctx.getLocalStorageBo().getFile());
            }

            // 注意放在此处会无可避免重复推送和注册，注意做好并发和幂等
            eventPublisher.publishEvent(new ArchiveUpdateEvent(this)
                    .setRecord(record)
                    .setFilename(ctx.getVideoFileName()));

        }
    }

    private List<Consumer<CmArchiveContextBo>> genPipeline(boolean isAuthFree, Integer stage) {
        final Stream<Consumer<CmArchiveContextBo>> stream;
        if (Objects.isNull(stage)) {
            // 全流程
            stream = Stream.concat(stage0(), stage1(isAuthFree));
        } else if (Objects.equals(stage, 0)) {
            // 页面流程的上传视频阶段
            stream = stage0();
        } else if (Objects.equals(stage, 1)) {
            // 页面流程的提交阶段
            stream = stage1(isAuthFree);
        } else {
            throw new IllegalArgumentException("无法识别pipeline stage " + stage);
        }
        return stream.collect(Collectors.toList());
    }

    /***
     当前的workflow较为粗糙，不支持必行，不支持DAG, 为了后来人，这里简单画一下流程

     submit-> download -> triggerXcode -> fetchCover -> waitForXcodePreview -> waitForXcodeMeta -> createArchive ->
     |                                  |
     |-> submit/probeAlgoVideoCover  -->
     *
     * @return
     */
    private Stream<Consumer<CmArchiveContextBo>> stage0() {

        return Stream.of(
                this::download,
                this::triggerXcode,
                this::fetchCover,
                this::waitForXcodePreview,
                this::waitForXcodeMeta
        );
    }

    private Stream<Consumer<CmArchiveContextBo>> stage1(boolean isAuthFree) {
        if (isAuthFree) return Stream.of(
                this::createArchive,
                this::archivePostProcess
        );

        return Stream.of(this::auth);
    }

    private void download(CmArchiveContextBo ctx) {
        final var record = ctx.getRecord();
        // 如果已经完成了视频云转码, 就不必重新下载
        if (record.getPipelineStage() >= CmArchivePipelineStage.FETCH_COVER) return;

        try {
            setPipelineStatusAsProcessing(record);

            VideoDownloadResult downloadResult = videoTools.download(
                    ctx.isDownloadFromMd5(), ctx.getVideoMd5(), ctx.getVideoUrl(), ctx.getSignature(),
                    ctx.getMaxSizeKb());

            final LocalStorageBo localStorageBo = downloadResult.getLocalStorageBo();
            ctx.setLocalStorageBo(localStorageBo);
            final var videoMd5 = localStorageBo.getMd5();

            final var mgkCmVideoRecord = mgkCmVideoDaoService.fetchByMd5(videoMd5);
            if (Objects.isNull(mgkCmVideoRecord)) {
                mgkCmVideoDaoService.newRecord(ctx.getVideoFileName(), ctx.getVideoUrl(), videoMd5, localStorageBo.getSizeInKb());
            }
            if (!StringUtils.hasText(record.getVideoMd5())) {
                record.setVideoMd5(videoMd5);
            }
            if (Objects.equals(record.getSizeKb(), 0)) {
                record.setSizeKb(localStorageBo.getSizeInKb());
            }
            record.setPipelineStage(CmArchivePipelineStage.DOWNLOAD)
                    .store();
            log.info("{}: download ok, signature={}", ID, ctx.getSignature());
        } catch (ArchivePipelineException e) {
            throw e;
        } catch (Throwable t) {
            throw new ArchivePipelineException(ArchivePipelineServerCodes.VIDEO_DOWNLOAD_FAILED, ctx.getSignature(), t);
        }
    }

    private void triggerXcode(CmArchiveContextBo ctx) {
        final var record = ctx.getRecord();
        if (record.getPipelineStage() >= CmArchivePipelineStage.TRIGGER_XCODE) return;

        try {
            setPipelineStatusAsProcessing(record);
            final var responseBo = biliUposService.ugc(ctx.getLocalStorageBo().getFile(), record.getMid());
            record.setUgcFilename(BiliUposService.fetchFileNameV2(responseBo.getUposUri()))
                    .setCid(responseBo.getBizId())
                    .setPipelineStage(CmArchivePipelineStage.TRIGGER_XCODE)
                    .store();
            log.info("{}: trigger xcode ok, signature={}", ID, ctx.getSignature());
        } catch (ArchivePipelineException e) {
            e.setSignature(ctx.getSignature());
            throw e;
        } catch (Throwable t) {
            throw new ArchivePipelineException(ArchivePipelineServerCodes.XCODE_REQUEST_FAILED, ctx.getSignature(), t);
        }
    }

    private void waitForXcodePreview(CmArchiveContextBo ctx) {
        final var record = ctx.getRecord();
        if (record.getPipelineStage() >= CmArchivePipelineStage.WAIT_FOR_XCODE_PREVIEW) return;

        try {
            setPipelineStatusAsProcessing(record);
            final var bvcFlowInfo = FunctionUtils.execWithRetires(BiliBvcFlowService.ID,
                    () -> biliBvcFlowService.fetchBvcFlowInfo(record.getUgcFilename()),
                    bvcXCodePreviewPollMaxAttempts,
                    Duration.ofSeconds(bvcXcodePreviewPollAttemptIntervalSeconds));
            Assert.notNull(bvcFlowInfo, "转码预览结果不能为空");
            record.setPublicEncodedUrl(bvcFlowInfo.getPublicSignedUrl())
                    .setPipelineStage(CmArchivePipelineStage.WAIT_FOR_XCODE_PREVIEW)
                    .store();
            log.info("{}: wait for xcode preview ok, signature={}", ID, ctx.getSignature());
        } catch (Throwable t) {
            throw new ArchivePipelineException(ArchivePipelineServerCodes.XCODE_QUERY_PREVIEW_FAILED, ctx.getSignature(), t);
        }
    }

    private void waitForXcodeMeta(CmArchiveContextBo ctx) {
        final var record = ctx.getRecord();
        if (record.getPipelineStage() >= CmArchivePipelineStage.WAIT_FOR_XCODE_META) return;

        try {
            setPipelineStatusAsProcessing(record);
            final var meta = FunctionUtils.execWithRetires(BiliBvcFlowService.ID, () -> biliBvcFlowService.fetchBvcFlowMeta(record.getUgcFilename()), 3, Duration.ofMillis(3));
            Assert.notNull(meta, "转码meta信息不能为空");
            final var gcd = NumberUtils.gcd(meta.getWidth(), meta.getHeight());
            record.setDurationInMs(meta.getDuration().intValue() * 1000)
                    .setWidth(meta.getWidth())
                    .setHeight(meta.getHeight())
                    .setRatioWidth(meta.getWidth() / gcd)
                    .setRatioHeight(meta.getHeight() / gcd)
                    .setSizeType(SizeType.getSizeType(meta.getWidth(), meta.getHeight()))
                    .setAuditStatus(ArchivePipelineServerCodes.WAITING_SUBMIT.getCode())
                    .setXcodeStatus(XcodeStatus.XCODE_SUCCEEDED)
                    .setPipelineStage(CmArchivePipelineStage.WAIT_FOR_XCODE_META)
                    .store();
            log.info("{}: wait for xcode meta ok, signature={}", ID, ctx.getSignature());
        } catch (Throwable t) {
            throw new ArchivePipelineException(ArchivePipelineServerCodes.XCODE_QUERY_META_FAILED, ctx.getSignature(), t);
        }
    }

    private void fetchCover(CmArchiveContextBo ctx) {
        final var record = ctx.getRecord();
        if (record.getPipelineStage() >= CmArchivePipelineStage.FETCH_COVER) return;

        try {
            setPipelineStatusAsProcessing(record);

            FetchCoverResult fetchCover = videoTools.fetchFirstFrameAsCover(
                    ctx.getCoverUrl(), ctx.isDownloadFromMd5(), ctx.getVideoMd5(), ctx.getVideoUrl(),
                    ctx.getVideoFileName(), ctx.getLocalStorageBo(), ctx.getSignature());


            // 这里保存了图片，允许了
            CoverBo customCoverOrFirstFrameCover = CoverBo.builder()
                    .md5(fetchCover.getCoverMd5())
                    .url(fetchCover.getCoverUrl())
                    .source(fetchCover.getCoverOrigin())
                    .build();

            // 根据mapi或者页面流量区分等待时间
            Optional<CoverBo> algoCover = Optional
                    .ofNullable(ctx.getAlgoCoverFuture())
                    .map(f -> {
                        if (ctx.isMapi()) {
                            try {
                                return f.get(algoVideoCoverConfig.getAlgoCoverWaitTimeoutSeconds4Mapi(),
                                        TimeUnit.SECONDS);
                            } catch (ExecutionException | InterruptedException | TimeoutException e) {
                                log.error("Fail to get algo video cover for mapi, only return the first-frame cover, "
                                        + "pipelineId={}", ctx.getUniqueId(), e);

                                Try.run(() -> f.cancel(true));
                                return null;
                            }
                        } else {
                            // 创意中心web端请求，推荐封面是必选项，
                            try {
                                return f.get(algoVideoCoverConfig.getAlgoCoverWaitTimeoutSeconds(), TimeUnit.SECONDS);
                            } catch (InterruptedException | ExecutionException | TimeoutException e) {
                                log.error("Fail to get algo video cover, pipelineId={}", ctx.getUniqueId(), e);
                                Try.run(() -> f.cancel(true));
                                throw new RuntimeException(e);
                            }
                        }
                    });

            MgkArchiveRecordExtra extra = new MgkArchiveRecordExtra()
                    .setIsMapi(ctx.isMapi())
                    .setRecommendCovers(List.of(customCoverOrFirstFrameCover));


            // 优先使用算法封面
            algoCover.ifPresent(algo -> {
                extra.setRecommendCovers(List.of(algo, customCoverOrFirstFrameCover));
            });


            // 目前固定选算法的
            CoverBo selectCover = ((Supplier<CoverBo>) () -> {
                if (Objects.equals(customCoverOrFirstFrameCover.getSource(),
                        ArchiveCoverOriginEnum.CUSTOM.getCode())) {
                    return customCoverOrFirstFrameCover;
                }

                return algoCover.orElse(customCoverOrFirstFrameCover);
            }).get();

            record.setCoverUrl(selectCover.getUrl())
                    .setCoverMd5(selectCover.getMd5())
                    .setPipelineStage(CmArchivePipelineStage.FETCH_COVER)
                    .setCoverOrigin(selectCover.getSource())
                    .setExtra(JsonUtil.writeValueAsString(extra))
                    .store();

            log.info("{}: fetch cover ok, signature={}", ID, ctx.getSignature());
        } catch (Throwable t) {
            throw new ArchivePipelineException(ArchivePipelineServerCodes.FETCH_COVER_FAILED, ctx.getSignature(), t);
        }
    }

    private void auth(CmArchiveContextBo ctx) {
        final var record = ctx.getRecord();
        if (record.getPipelineStage() >= CmArchivePipelineStage.TRIGGER_AUTH) return;

        try {
            setPipelineStatusAsProcessing(record);
            notifyOnSubmittingArchive(record, false);
            record.setAuthStatus(AuthStatus.WAITING)
                    .setAuditStatus(ArchivePipelineServerCodes.WAITING_AUTH.getCode())
                    .setAuthTime(NumberUtils.zeroValueTimestamp())
                    .setPipelineStage(CmArchivePipelineStage.TRIGGER_AUTH)
                    .store();
            log.info("{}: auth ok, signature={}", ID, ctx.getSignature());
        } catch (Throwable t) {
            throw new ArchivePipelineException(ArchivePipelineServerCodes.AUTH_FAILED, ctx.getSignature(), t);
        }
    }

    private void createArchive(CmArchiveContextBo ctx) {
        final var record = ctx.getRecord();
        if (record.getPipelineStage() >= CmArchivePipelineStage.CREATE_ARCHIVE) return;

        try {
            setPipelineStatusAsProcessing(record);
            final var upFrom = ArchiveMode.isUserSpace(record.getArchiveMode()) ? UpFrom.USER_SPACE : UpFrom.CM_SPACE;
            final var avid = biliArchiveService.createArchive(record.getMid(), record.getCid(), record.getUgcFilename(), record.getCatTwo(), record.getCoverUrl(), record.getTitle(), record.getTags(), upFrom, record.getIpv6());
            record.setAvid(avid)
                    .setAuditStatus(ArchivePipelineServerCodes.WAITING_AUDIT.getCode())
                    .setPipelineStage(CmArchivePipelineStage.CREATE_ARCHIVE)
                    .store();
            log.info("{}: create archive ok, signature={}", ID, ctx.getSignature());
        } catch (ArchivePipelineException e) {
            if (Objects.equals(e.getCode(), ArchivePipelineServerCodes.UGC_CID_EXPIRED.getCode())) {
                // cid过期
                record.setCid(0L)
                        .setPipelineStage(AdxArchivePipelineStage.DOWNLOAD)
                        .store();
            }
            throw new ArchivePipelineException(ServerCode.create(ArchivePipelineServerCodes.BILI_CREATE_ARCHIVE_FAILED.getCode(), e.getMessage()), ctx.getSignature(), e);
        } catch (Throwable t) {
//            throw new ArchivePipelineException(ArchivePipelineServerCodes.BILI_CREATE_ARCHIVE_FAILED, ctx.getSignature(), t);
            throw new ArchivePipelineException(ServerCode.create(ArchivePipelineServerCodes.BILI_CREATE_ARCHIVE_FAILED.getCode(), t.getMessage()), ctx.getSignature(), t);
        }
    }

    private void archivePostProcess(CmArchiveContextBo ctx) {
        final var record = ctx.getRecord();
        if (record.getPipelineStage() >= CmArchivePipelineStage.ARCHIVE_POST_PROCESS) return;


        try {
            setPipelineStatusAsProcessing(record);
            if (ArchiveMode.isCmSpace(record.getArchiveMode())) {
                biliArchiveService.disableDanmaku(record.getCid());
            } else if (ArchiveMode.isUserSpace(record.getArchiveMode())) {
                notifyOnSubmittingArchive(record, true);
            }
            if (ctx.isDisableExternalAccess()) {
                saveExtra(record.getAvid());
            }
            record.setPipelineStage(CmArchivePipelineStage.ARCHIVE_POST_PROCESS)
                    .setAuditStatus(ArchivePipelineServerCodes.WAITING_AUDIT.getCode())
                    .store();
            log.info("{}: post process ok, signature={}", ID, ctx.getSignature());
        } catch (Throwable t) {
            throw new ArchivePipelineException(ArchivePipelineServerCodes.ARCHIVE_POST_PROCESS_FAILED, ctx.getSignature(), t);
        }

        //主动开启 or 商业小号
        if (Boolean.TRUE.equals(ctx.getEnableSelectiveComment()) || ArchiveMode.isCmSpace(record.getArchiveMode())) {
            log.info("新增开启评论, 开启精选评论记录: mid={}, avid={}", record.getMid(), record.getAvid());
            cmArchiveCommentService.recordForLaterProcess(record.getMid(), record.getAvid());
        }
    }

    private void saveExtra(long avid) {
        biliArchiveExtraService.saveExtra(avid, "sycpb_archive_pipeline", CmArchiveExtraBo.builder().disableExternalAccess(true).build());
    }

    private void notifyOnSubmittingArchive(MgkCmArchiveRecord record, boolean authFree) {
        final var accountRecord = accountService.fetch(record.getAccountId());
        final var url = UriComponentsBuilder.fromUriString(imRpcService.getAuthUrl())
                .replaceQueryParam("key", NumberUtils.radix62(record.getCid()))
                .build()
                .toString();
        imRpcService.sendMessage(accountService.fetchAgentName(accountRecord.getDependencyAgentId()), accountService.fetchCustomerName(accountRecord.getCustomerId()), url, record.getMid(), authFree);
    }

    private void setPipelineStatusAsProcessing(MgkCmArchiveRecord record) {
        record.setPipelineStatus(ArchivePipelineStatus.PROCESSING)
                .store();
    }

    private String genSignature(CmArchiveContextBo ctx) {
        return "archive_mode=" + ctx.getArchiveMode()
                + "&account_id=" + ctx.getAccountId()
                + "&video_md5=" + ctx.getVideoMd5()
                + "&mid=" + ctx.getMid()
                + "&source=" + ctx.getSource()
                + "&unique_id=" + ctx.getUniqueId()
                + "&video_url=" + ctx.getVideoUrl()
                + "&title=" + ctx.getTitle();
    }

    private String genSignature(MgkCmArchiveRecord record) {
        return "archive_mode=" + record.getArchiveMode()
                + "&account_id=" + record.getAccountId()
                + "&video_md5=" + record.getVideoMd5()
                + "&mid=" + record.getMid()
                + "&source=" + record.getSource()
                + "&unique_id=" + record.getUniqueId()
                + "&video_url=" + record.getVideoUrl()
                + "&title=" + record.getTitle();
    }

    private RLock getLock(String uniqueId) {
        return redissonClient.getLock("scv_cm_archive_working_" + uniqueId);
    }
}
