package com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2;

import com.bapis.ad.mgk.material.MaterialIdRegisterResp;
import com.bapis.ad.mgk.material.MaterialIdResp;
import com.bapis.ad.mgk.material.MaterialIdServiceGrpc;
import com.bapis.ad.mgk.material.MaterialUkReq;
import com.bilibili.databus.base.Message;
import com.bilibili.databus.core.DataBusClient;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.event.ArchiveAggregationUpdateEvent;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.event.MaterialVideo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.model.ArchiveModelConvertor;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.MgkCmArchiveAccountMappingDaoService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.MgkCmArchiveDaoService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.generated.tables.records.MgkCmArchiveAccountMappingRecord;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.generated.tables.records.MgkCmArchiveRecord;
import com.bilibili.sycpb.cpm.scv.app.utils.constants.ArchiveMode;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.common.collect.Lists;
import io.vavr.control.Try;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import pleiades.venus.starter.rpc.client.RPCClient;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/4/28
 */
@Slf4j
@Service
public class MaterialAggregationViewUpdateEventPublisher {

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @RPCClient("sycpb.cpm.mgk-portal")
    private MaterialIdServiceGrpc.MaterialIdServiceBlockingStub materialIdService;

    @Resource
    private MgkCmArchiveDaoService mgkCmArchiveDaoService;

    @Resource
    private MgkCmArchiveAccountMappingDaoService mgkCmArchiveAccountMappingDaoService;

    @Resource
    private MaterialAggregationLock materialAggregationLock;

    @Resource
    private DataBusClient videoAggregationViewUpdatePub;


    private final ObjectMapper objectMapper = new ObjectMapper()
            .registerModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .configure(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES, false)
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
            .setSerializationInclusion(JsonInclude.Include.NON_NULL);


    public String onRecordUpdated(MgkCmArchiveRecord record, String materialId) {

        // 仅关注小号视频，小号视频才是所谓的”视频“素材，其他的为”稿件"素材 , 两者的区分是一个是md5去重，一个是avid去重

        if (ArchiveMode.isUserSpace(record.getArchiveMode())) {

            log.info("Skip bili-archive of mgk_cm_archive. record.md5={}, avid={}, ", record.getVideoMd5(),
                    record.getAvid());
            return materialId;
        }

        if (materialId == null) {

            materialId = Try.of(() -> {

                // 理论上此时已经完成注册。

                MaterialIdResp r = materialIdService.findByTypeAndUks(
                        MaterialUkReq.newBuilder().setMaterialIdType("video")
                                .addAllMaterialUks(Lists.newArrayList(record.getVideoMd5()))
                                .build()
                );

                if (r == null || r.getMaterialIdsMap().isEmpty()) {
                    throw new RuntimeException("fail to find materialId, videoMd5=" + record.getVideoMd5());
                }
                return r.getMaterialIdsMap().get(record.getVideoMd5()).getMaterialId();

            }).getOrElseGet(t -> {

                return Try.of(() -> {
                    MaterialIdRegisterResp r = materialIdService.register(
                            ArchiveModelConvertor.instance.record2MaterialRegisterReq(record, "")
                    );

                    return r.getMaterialId();

                }).onFailure(t2 -> {
                    log.error("Fail to register materialId, videoMd5=" + record.getVideoMd5(), t2);
                }).getOrNull();
            });
        }

        if (materialId == null) {
            log.error("Fail to get materialId for bili-archive of mgk_cm_archive. record.md5={}, avid={}, ",
                    record.getVideoMd5(),
                    record.getAvid());

            return materialId;
        }

        this.triggerMaterialAggregationUpdateEvent(record, materialId);

        return materialId;
    }


    public void triggerMaterialAggregationUpdateEvent(MgkCmArchiveRecord record, String materialId) {

        // Notice: 是否需要加锁？ 理论上过程中先查所有的快照，再进行推送，需要保证有序。有可能先发生的快照后推送。导致乱序。会牺牲性能。

        Try.run(() -> {

            materialAggregationLock.lockProduceProcedure(materialId, () -> {

                List<MaterialVideo> sameVideoHolder = mgkCmArchiveDaoService.listByAvidOrMd5(
                                new ArrayList<>(),
                                Lists.newArrayList(record.getVideoMd5()))
                        .stream()
                        .filter(a -> a.getIsDeleted() == 0)
                        .filter(a -> !ArchiveMode.isUserSpace(a.getArchiveMode()))
                        .map(a -> ArchiveModelConvertor.instance.record2MaterialVideoWithNoShareTargetId(a, materialId))
                        .collect(Collectors.toList());

                Map<Long, List<MgkCmArchiveAccountMappingRecord>> sameVideoShareMapping =
                        mgkCmArchiveAccountMappingDaoService.listByAvids(
                                sameVideoHolder.stream().map(v -> v.getAvid()).filter(avid -> avid != 0)
                                        .distinct().collect(Collectors.toList())
                        ).stream().collect(Collectors.groupingBy(r -> r.getAvid()));

                sameVideoHolder.stream().forEach(v -> {

                    v.setShareTargetAccountIds(Optional.ofNullable(sameVideoShareMapping.get(v.getAvid()))
                            .orElse(new ArrayList<>())
                            .stream().map(m -> m.getAccountId())
                            .distinct()
                            .collect(Collectors.toList())
                    );

                });

                applicationEventPublisher.publishEvent(new ArchiveAggregationUpdateEvent(this)
                        .setMaterialId(materialId)
                        .setMaterialUk(record.getVideoMd5())
                        .setArchives(sameVideoHolder)

                );
                return null;
            });
        }).onFailure(t -> {
            log.error("Fail to push video aggregation-view update event, materialId={}, record-id={}",
                    materialId, record.getId(), t);
        });
    }


    @EventListener(classes = ArchiveAggregationUpdateEvent.class)
    public void onEvent(ArchiveAggregationUpdateEvent event) {

        String msg = Try.of(() -> objectMapper.writeValueAsString(VideoAggregationDatabusMsg.fromEvent(event)))
                .getOrElseThrow(t -> {
                    log.error("Fail to serialize event to json, event={}", event, t);
                    return new RuntimeException("Fail to serialize event to json", t);
                });
        // 使用databus延迟队列进行调度；
        Message message = new Message(event.getMaterialId(), msg.getBytes(),
                new HashMap<>());

        Try.run(() -> videoAggregationViewUpdatePub.pub(message)
        ).onFailure(t -> {

            log.error("Fail to publish video aggregation event to databus, event={}", event, t);
        }).onSuccess(r -> {
            log.info("Success to publish video aggregation event to databus, event={}", event);
        });

    }


    @Data
    @Accessors(chain = true)
    public static class VideoAggregationDatabusMsg {

        private String materialId;

        private String materialType = "video";

        private String materialUk;

        private List<MaterialVideo> archives;


        public static VideoAggregationDatabusMsg fromEvent(ArchiveAggregationUpdateEvent event) {

            return new VideoAggregationDatabusMsg()
                    .setMaterialId(event.getMaterialId())
                    .setMaterialUk(event.getMaterialUk())
                    .setMaterialType(event.getMaterialType())
                    .setArchives(event.getArchives());
        }

    }





}
