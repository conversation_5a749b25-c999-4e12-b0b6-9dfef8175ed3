package com.bilibili.sycpb.cpm.scv.app.service.databus.v1.watermark_erase;

import com.bilibili.databus.core.DataBusClient;
import com.bilibili.discovery.DiscoveryClient;
import com.bilibili.sycpb.cpm.scv.app.service.adhoc.RefreshArchiveIfUsingFirstFrameAsCoverService;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm.CmArchivePipelineServiceWatermarkEraseImpl;
import com.bilibili.sycpb.cpm.scv.app.service.databus.v1.watermark_erase.DatabusProperty.Pub;
import com.bilibili.sycpb.cpm.scv.app.service.databus.v1.watermark_erase.DatabusProperty.Sub;
import io.opentelemetry.api.OpenTelemetry;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.PropertySource;
import pleiades.venus.starter.paladin.PaladinPropertySourceFactory;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/23
 */
@Slf4j
@Configuration
@PropertySource(value = "classpath:databus.yml", factory = PaladinPropertySourceFactory.class)
public class WatermarkErasePipelineDatabusConfig implements DatabusConfigSupport {

    @Resource
    private DiscoveryClient discoveryClient;

    @Resource
    private OpenTelemetry discoveryNoopOpenTelemetry;

    @Lazy
    @Resource
    private CmArchivePipelineServiceWatermarkEraseImpl pipelineServiceWatermarkErase;

    @Lazy
    @Resource
    private RefreshArchiveIfUsingFirstFrameAsCoverService smartCoverRefreshJobService;


    @Bean
    @ConfigurationProperties(prefix = "warp.databus")
    public DatabusProperties manualWarpDatabusProperties() {
        return new DatabusProperties();
    }


    @Bean
    public DataBusClient watermarkErasePub(DatabusProperties manualWarpDatabusProperties) {

        DatabusProperty property = manualWarpDatabusProperties.getProperties()
                .get("watermark-erase");

        Pub pubProperty = property.getPub();

        return createDatabusPubV3(
                property.getTopic(), pubProperty.getGroup(), pubProperty.getAppId(), pubProperty.getZone(),
                pubProperty.getToken(), discoveryClient, discoveryNoopOpenTelemetry
        );
    }


    @Bean
    public List<DataBusClient> watermarkEraseSub(DatabusProperties manualWarpDatabusProperties) {
        DatabusProperty property = manualWarpDatabusProperties.getProperties()
                .get("watermark-erase");

        Sub subProperty = property.getSub();
        return createDatabusSubV3AndStartSub(
                property.getTopic(), subProperty.getGroup(), subProperty.getAppId(), subProperty.getZone(),
                subProperty.getToken(), subProperty.getConsumer(),
                msg -> pipelineServiceWatermarkErase.runWatermarkEraseJob(msg), discoveryClient,
                discoveryNoopOpenTelemetry
        );
    }




    @Data
    public static class DatabusProperties {

        private Map<String, DatabusProperty> properties = new HashMap<>();

        public Map<String, DatabusProperty> getProperties() {
            return properties;
        }

        public void setProperties(Map<String, DatabusProperty> properties) {
            this.properties = properties;
        }

    }

}