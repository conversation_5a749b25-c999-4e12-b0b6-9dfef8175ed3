package com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive;

import com.alibaba.fastjson.JSON;
import com.bilibili.sycpb.cpm.scv.app.controller.open.lib.archive.bos.ArchivePushBo;
import com.bilibili.sycpb.cpm.scv.app.controller.open.lib.archive.bos.ArchivePushFailInfoBo;
import com.bilibili.sycpb.cpm.scv.app.controller.open.lib.archive.bos.ArchivePushResultV2Bo;
import com.bilibili.sycpb.cpm.scv.app.controller.open.lib.archive.bos.SimpleAccountBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.account.CorpAccountQuerier;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.MaterialAggregationViewUpdateEventPublisher;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.MgkCmArchiveAccountMappingDaoService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.MgkCmArchiveDaoService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.MgkDataSourceConfig;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.generated.tables.records.MgkCmArchiveAccountMappingRecord;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.generated.tables.records.MgkCmArchiveRecord;
import com.bilibili.sycpb.cpm.scv.app.utils.NumberUtils;
import com.google.common.collect.Lists;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class CmArchivePushSyncer {

    @Value("${material.push.limit.account:20}")
    private Integer accountLimit;
    @Value("${material.push.limit.media:100}")
    private Integer mediaLimit;

    @Value("${mapi.material.push.limit.account:50}")
    private Integer mapiAccountLimit;
    @Value("${mapi.material.push.limit.media:50}")
    private Integer mapiMediaLimit;
    @Value("${material.push.limit.total.media:500}")
    private Integer totalMediaLimit;
    @Value("${material.push.limit.single.account.media:20000}")
    private Integer singleAccountMediaLimit;

    private static final int PERSONAL_FLY_USER_TYPE = 2;

    public static final int BATCH_SIZE = 50;

    private static final String SAME_ENTITY_OR_AUTHOR_FAIL_REASON = "目标账户非同主体或权限不足";
    private static final String MEDIA_NUM_LIMIT_FAIL_REASON = "目标账户素材库超上限";
    private static final String NOT_PASSED_CM_SPACE_ARC_FAIL_REASON = "非商业小号稿件或未审核通过";
    private static final String AUTH_INVALID_ARC_FAIL_REASON = "稿件不属于当前账户";

    @Autowired
    private MgkCmArchiveDaoService mgkCmArchiveDaoService;
    @Autowired
    private MgkCmArchiveAccountMappingDaoService mgkCmArchiveAccountMappingDaoService;
    @Autowired
    private CorpAccountQuerier corpAccountQuerier;

    @Autowired
    private MaterialAggregationViewUpdateEventPublisher materialAggregationViewUpdateEventPublisher;

    @Transactional(MgkDataSourceConfig.MGK_TX_MGR)
    public ArchivePushResultV2Bo batchPush(ArchivePushBo mediaPushDto) {
        Assert.notNull(mediaPushDto, "mediaPushDto 参数错误");

        log.info("batch push media data, operatorId:{}, mediaPushDto:{}", mediaPushDto.getCurAccountId(), JSON.toJSONString(mediaPushDto));
        Assert.notNull(mediaPushDto.getCurAccountId(), "operator id 参数错误");
        Assert.isTrue(!CollectionUtils.isEmpty(mediaPushDto.getNeedSyncIds()), "mediaIds 不能为空");
        Assert.isTrue(!CollectionUtils.isEmpty(mediaPushDto.getNeedSyncAccountIds()), "同步的 accountIds 不能为空");
        Assert.isTrue(!mediaPushDto.getNeedSyncAccountIds().contains(mediaPushDto.getCurAccountId()), "不允许推送给当前账户");

        Integer realAccountLimit = mediaPushDto.isOpenApi() ? mapiAccountLimit : accountLimit;
        Integer realMediaLimit = mediaPushDto.isOpenApi() ? mapiMediaLimit : mediaLimit;

        // 推送上限
        if (mediaPushDto.getNeedSyncAccountIds().size() > realAccountLimit) {
            throw new IllegalArgumentException("推送的账户数量不能超过:" + realAccountLimit);
        }
        if (mediaPushDto.getNeedSyncIds().size() > realMediaLimit) {
            throw new IllegalArgumentException("推送的素材数量不能超过:" + realMediaLimit);
        }
        int totalCount = mediaPushDto.getNeedSyncIds().size() * mediaPushDto.getNeedSyncAccountIds().size();
        if (totalCount > totalMediaLimit) {
            throw new IllegalArgumentException("单次推送账户x素材总数超上限:" + totalMediaLimit);
        }

        List<ArchivePushFailInfoBo> failInfoBoList = new ArrayList<>();
        ArchivePushResultV2Bo resultBo = ArchivePushResultV2Bo.builder()
                .failList(failInfoBoList)
                .build();
        // 检查是否同一个集团，同一个产品下的
        List<Integer> queryAccountIds = Lists.newArrayList(mediaPushDto.getNeedSyncAccountIds());
        queryAccountIds.add(mediaPushDto.getCurAccountId());
        List<SimpleAccountBo> simpleAccountBos = corpAccountQuerier.listAccount(queryAccountIds);
        Map<Integer, String> accountNameMap = simpleAccountBos.stream()
                .collect(Collectors.toMap(SimpleAccountBo::getAccountId, SimpleAccountBo::getUsername));

        List<Integer> validPushAccountIds = getValidPushAccountIds(mediaPushDto.getCurAccountId(), simpleAccountBos);
        List<ArchivePushFailInfoBo> accountFailInfoList =
                generateFailInfoByAccountIds(mediaPushDto, validPushAccountIds, accountNameMap, SAME_ENTITY_OR_AUTHOR_FAIL_REASON);
        if (!CollectionUtils.isEmpty(accountFailInfoList)) {
            failInfoBoList.addAll(accountFailInfoList);
        }
        mediaPushDto.setNeedSyncAccountIds(validPushAccountIds);
        if (CollectionUtils.isEmpty(validPushAccountIds)) {
            return resultBo;
        }

        // 账户素材库数量校验
        Map<Integer, Integer> accountPushArcNumMap =
                mgkCmArchiveAccountMappingDaoService.countByAccountId(validPushAccountIds);
        List<Integer> validNumPushAccountIds = validPushAccountIds.stream()
                .filter(accountId -> !accountPushArcNumMap.containsKey(accountId)
                        || accountPushArcNumMap.get(accountId) <= singleAccountMediaLimit)
                .collect(Collectors.toList());
        List<ArchivePushFailInfoBo> mediaNumAccountFailInfoList =
                generateFailInfoByAccountIds(mediaPushDto, validNumPushAccountIds, accountNameMap, MEDIA_NUM_LIMIT_FAIL_REASON);
        if (!CollectionUtils.isEmpty(mediaNumAccountFailInfoList)) {
            failInfoBoList.addAll(mediaNumAccountFailInfoList);
        }
        mediaPushDto.setNeedSyncAccountIds(validNumPushAccountIds);
        if (CollectionUtils.isEmpty(mediaPushDto.getNeedSyncAccountIds())) {
            return resultBo;
        }

        Set<Long> validAvidSet = mediaPushDto.getNeedSyncIds().stream()
                .filter(NumberUtils::isPositive)
                .collect(Collectors.toSet());
        List<ArchivePushFailInfoBo> invalidAvidFailInfoList =
                generateFailInfoByValidAvids(mediaPushDto, validAvidSet, accountNameMap, NOT_PASSED_CM_SPACE_ARC_FAIL_REASON);
        if (!CollectionUtils.isEmpty(invalidAvidFailInfoList)) {
            failInfoBoList.addAll(invalidAvidFailInfoList);
        }
        mediaPushDto.setNeedSyncIds(new ArrayList<>(validAvidSet));
        if (CollectionUtils.isEmpty(validAvidSet)) {
            // 为了创意中心界面逻辑 先报错
            throw new IllegalArgumentException(NOT_PASSED_CM_SPACE_ARC_FAIL_REASON);
        }

        // 是否都是小号稿件且审核通过 且avid > 0
        List<MgkCmArchiveRecord> baseArchiveRecords = mgkCmArchiveDaoService.queryCmArchiveByAvids(mediaPushDto.getNeedSyncIds());
        List<MgkCmArchiveRecord> needSyncArchiveRecords = baseArchiveRecords.stream()
                .filter(record -> record.getAuditStatus() >= 0)
                .filter(record -> record.getAvid() > 0)
                .collect(Collectors.toList());
        Set<Long> passedCmSpaceAvidSet = needSyncArchiveRecords.stream()
                .map(MgkCmArchiveRecord::getAvid)
                .collect(Collectors.toSet());
        List<ArchivePushFailInfoBo> notPassedCmSpaceFailInfoList =
                generateFailInfoByValidAvids(mediaPushDto, passedCmSpaceAvidSet, accountNameMap, NOT_PASSED_CM_SPACE_ARC_FAIL_REASON);
        if (!CollectionUtils.isEmpty(notPassedCmSpaceFailInfoList)) {
            failInfoBoList.addAll(notPassedCmSpaceFailInfoList);
        }
        mediaPushDto.setNeedSyncIds(new ArrayList<>(passedCmSpaceAvidSet));
        if (CollectionUtils.isEmpty(passedCmSpaceAvidSet)) {
            // 为了创意中心界面逻辑 先报错
            throw new IllegalArgumentException(NOT_PASSED_CM_SPACE_ARC_FAIL_REASON);
//            return resultBo;
        }

        // 这些素材是否自己拥有(稿件表和副本关系)
        List<MgkCmArchiveAccountMappingRecord> mappingRecords =
                mgkCmArchiveAccountMappingDaoService.listByAccountIdAndAvids(
                        Lists.newArrayList(mediaPushDto.getCurAccountId()),
                        mediaPushDto.getNeedSyncIds());
        Set<Long> hasAuthAvidSet = mappingRecords.stream().map(MgkCmArchiveAccountMappingRecord::getAvid)
                .collect(Collectors.toSet());
        Set<Long> hasUploadAuthAvidSet = needSyncArchiveRecords.stream()
                .filter(record -> Objects.equals(record.getAccountId(), mediaPushDto.getCurAccountId()))
                .map(MgkCmArchiveRecord::getAvid)
                .collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(hasUploadAuthAvidSet)) {
            hasAuthAvidSet.addAll(hasUploadAuthAvidSet);
        }

        List<ArchivePushFailInfoBo> notHasAuthCmSpaceFailInfoList =
                generateFailInfoByValidAvids(mediaPushDto, hasAuthAvidSet, accountNameMap, AUTH_INVALID_ARC_FAIL_REASON);
        if (!CollectionUtils.isEmpty(notHasAuthCmSpaceFailInfoList)) {
            failInfoBoList.addAll(notHasAuthCmSpaceFailInfoList);
        }
        mediaPushDto.setNeedSyncIds(new ArrayList<>(hasAuthAvidSet));
        if (CollectionUtils.isEmpty(hasAuthAvidSet)) {
            return resultBo;
        }

        // 生成需需要同步的素材
        List<MgkCmArchiveAccountMappingRecord> needAddPos = generateNeedSyncData(mediaPushDto, needSyncArchiveRecords);

        if (CollectionUtils.isEmpty(needAddPos)) {
            return resultBo;
        }

        // 批量插入
        Integer count = 0;
        List<List<MgkCmArchiveAccountMappingRecord>> partitions = Lists.partition(needAddPos, BATCH_SIZE);
        for (List<MgkCmArchiveAccountMappingRecord> partition : partitions) {
            count += mgkCmArchiveAccountMappingDaoService.batchCreate(partition);
        }

        needSyncArchiveRecords.forEach(record -> {
            materialAggregationViewUpdateEventPublisher.onRecordUpdated(record, null);
        });




        log.info("batch push media data, operatorId:{}, sync size:{}", mediaPushDto.getCurAccountId(), count);
        return resultBo;
    }

    /**
     * 生成需要同步的数据
     */
    private List<MgkCmArchiveAccountMappingRecord> generateNeedSyncData(ArchivePushBo mediaPushDto, List<MgkCmArchiveRecord> needSyncArchiveRecords) {
        // 找出这些账户已经存在的稿件 mapping
        List<MgkCmArchiveAccountMappingRecord> mappingRecords = mgkCmArchiveAccountMappingDaoService.listByAccountIdAndAvids(mediaPushDto.getNeedSyncAccountIds(), mediaPushDto.getNeedSyncIds());
        // 去重 key set(accountId_avid)
        Set<String> existAccountAndMediaAvidSet = mappingRecords.stream().map(mgkCollageMediaPo -> String.format("%s_" +
            "%s", mgkCollageMediaPo.getAccountId(), mgkCollageMediaPo.getAvid())).collect(Collectors.toSet());
        Set<String> originAccountAndMediaAvidSet = needSyncArchiveRecords.stream()
                .map(record -> String.format("%s_%s", record.getAccountId(), record.getAvid()))
                .collect(Collectors.toSet());
        existAccountAndMediaAvidSet.addAll(originAccountAndMediaAvidSet);

        // 组装需要同步的数据 = 全量的叉乘 - 已经存在的
        List<MgkCmArchiveAccountMappingRecord> needAddPos = new ArrayList<>();

        // 需要同步的账号
        for (Integer needSyncAccountId : mediaPushDto.getNeedSyncAccountIds()) {
            // 需要同步的素材
            for (MgkCmArchiveRecord needSyncMediaPo : needSyncArchiveRecords) {
                String key = String.format("%s_%s", needSyncAccountId, needSyncMediaPo.getAvid());

                if (!existAccountAndMediaAvidSet.contains(key)) {
                    MgkCmArchiveAccountMappingRecord newMediaPo = new MgkCmArchiveAccountMappingRecord();
                    newMediaPo.setAccountId(needSyncAccountId);
                    newMediaPo.setFromAccountId(mediaPushDto.getCurAccountId());
                    newMediaPo.setAvid(needSyncMediaPo.getAvid());
                    newMediaPo.setCtime(Timestamp.from(Instant.now()));
                    newMediaPo.setMtime(Timestamp.from(Instant.now()));
                    needAddPos.add(newMediaPo);
                }
            }
        }
        log.info("generateNeedSyncMediaData, needSyncDataSize:{}", needAddPos.size());
        return needAddPos;
    }

    /**
     * 检查推送的账户是否同一个集团，同一个产品下的
     */
    private List<Integer> getValidPushAccountIds(Integer curAccountId, List<SimpleAccountBo> accountDtos) {
        Assert.notEmpty(accountDtos, "不存在有效账户");

        Map<Integer, SimpleAccountBo> accountIdMap = accountDtos.stream()
                .collect(Collectors.toMap(SimpleAccountBo::getAccountId, Function.identity()));

        SimpleAccountBo curAccountDto = accountIdMap.get(curAccountId);

        // 必须有集团有品牌
        if (!NumberUtils.isPositive(curAccountDto.getProductId())) {
            return Collections.emptyList();
        }

        if (!NumberUtils.isPositive(curAccountDto.getGroupId())) {
            return Collections.emptyList();
        }

        return accountDtos.stream()
                .filter(accountDto -> !Objects.equals(accountDto.getAccountId(), curAccountId))
                // 同集团同品牌
                .filter(accountDto -> Objects.equals(accountDto.getGroupId(), curAccountDto.getGroupId())
                        && Objects.equals(accountDto.getProductId(), curAccountDto.getProductId()))
                // 没代理 或者代理商一致
                .filter(accountDto -> !NumberUtils.isPositive(accountDto.getDependencyAgentId())
                        || Objects.equals(accountDto.getDependencyAgentId(), curAccountDto.getDependencyAgentId()))
                // 有三连权限
                .filter(accountDto -> Objects.equals(accountDto.getAdStatus(), 0)
                        || Objects.equals(accountDto.getIsSupportContent(), 1)
                        || Objects.equals(accountDto.getIsSupportFly(), 1)
                        && !Objects.equals(accountDto.getUserType(), PERSONAL_FLY_USER_TYPE))
                .map(SimpleAccountBo::getAccountId)
                .collect(Collectors.toList());
    }

    // 素材维度错误
    private List<ArchivePushFailInfoBo> generateFailInfoByValidAvids(ArchivePushBo pushBo,
                                                                         Set<Long> validPushAvIds,
                                                                         Map<Integer, String> accountNameMap,
                                                                         String failReason) {
        if (Objects.isNull(pushBo)) {
            return Collections.emptyList();
        }

        List<Long> failAvIds = pushBo.getNeedSyncIds().stream()
                .filter(avid -> !validPushAvIds.contains(avid))
                .collect(Collectors.toList());

        List<ArchivePushFailInfoBo> result = new ArrayList<>();

        pushBo.getNeedSyncAccountIds().forEach(accountId -> {
            String accountName = accountNameMap.getOrDefault(accountId, "未知账户" + accountId);
            failAvIds.forEach(failAvid -> {
                ArchivePushFailInfoBo failInfoDto = ArchivePushFailInfoBo.builder()
                        .videoId(failAvid.toString())
                        .advertiserId(accountId)
                        .advertiserName(accountName)
                        .failReason(failReason)
                        .build();
                result.add(failInfoDto);
            });
        });

        return result;
    }

    // 同主体/权限错误
    private List<ArchivePushFailInfoBo> generateFailInfoByAccountIds(ArchivePushBo pushBo,
                                                                    List<Integer> validPushAccountIds,
                                                                    Map<Integer, String> accountNameMap,
                                                                    String failReason) {
        if (Objects.isNull(pushBo)) {
            return Collections.emptyList();
        }

        List<Integer> invalidAccountIds = pushBo.getNeedSyncAccountIds().stream()
                .filter(accountId -> !validPushAccountIds.contains(accountId))
                .collect(Collectors.toList());

        List<ArchivePushFailInfoBo> result = new ArrayList<>();

        invalidAccountIds.forEach(invalidAccountId -> {
            String accountName = accountNameMap.getOrDefault(invalidAccountId, "未知账户" + invalidAccountId);
            pushBo.getNeedSyncIds().forEach(id -> {
                ArchivePushFailInfoBo failInfoDto = ArchivePushFailInfoBo.builder()
                        .videoId(id.toString())
                        .advertiserId(invalidAccountId)
                        .advertiserName(accountName)
                        .failReason(failReason)
                        .build();
                result.add(failInfoDto);
            });
        });
        return result;
    }
}
