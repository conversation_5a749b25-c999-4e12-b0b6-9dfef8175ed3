/*
 * Copyright (c) 2015-2022 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.service.xxl.tempus_jobs;

import com.bilibili.sycpb.cpm.scv.app.service.bili.archive.BiliArchiveService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.MgkDataSourceConfig;
import com.bilibili.sycpb.cpm.scv.app.service.rpc.CommunityRpcService;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.generated.Tables.MGK_CM_ARCHIVE;

/**
 * 禁用评论任务
 * 子账号投稿默认关闭评论区, 因为主站接口有概率会失败, 所以定时查看回刷
 */
@Slf4j
@Service
public class DisableCommentTempusJob implements BasicProcessor {
    
    public static final String ID = "DisableCommentTempusJob";
    private final DSLContext mgk;
    private final BiliArchiveService biliArchiveService;

    public DisableCommentTempusJob(@Qualifier(MgkDataSourceConfig.MGK_DSL_CONTEXT) DSLContext mgk,
                                   CommunityRpcService communityRpcService, 
                                   BiliArchiveService biliArchiveService) {
        this.mgk = mgk;
        this.biliArchiveService = biliArchiveService;
    }

    @Override
    public ProcessResult process(TaskContext context) throws Exception {
        OmsLogger logger = context.getOmsLogger();
        String jobParams = Optional.ofNullable(context.getJobParams()).orElse("");
        
        logger.info("DisableCommentTempusJob 开始执行, 参数: {}", jobParams);
        log.info("DisableCommentTempusJob 开始执行, 参数: {}", jobParams);
        
        try {
            final int mins;
            if (StringUtils.hasText(jobParams)) {
                mins = Integer.parseInt(jobParams);
                logger.info("使用参数指定的分钟数: {}", mins);
            } else {
                mins = 20;
                logger.info("使用默认分钟数: {}", mins);
            }
            
            final Timestamp startingTs = Timestamp.valueOf(LocalDateTime.now().minusMinutes(mins));
            logger.info("查询时间范围: 从 {} 开始", startingTs);
            
            final List<Long> avids = mgk.select(MGK_CM_ARCHIVE.AVID)
                .from(MGK_CM_ARCHIVE)
                .where(MGK_CM_ARCHIVE.CTIME.gt(startingTs)
                    .and(MGK_CM_ARCHIVE.AUDIT_STATUS.greaterOrEqual(0)))
                .fetch(MGK_CM_ARCHIVE.AVID);
                
            logger.info("找到需要处理的稿件数量: {}", avids.size());
            log.info("找到需要处理的稿件数量: {}", avids.size());
            
            int processedCount = 0;
            for (var avid : avids) {
                try {
                    biliArchiveService.checkAndDisableComment(avid);
                    processedCount++;
                } catch (Exception e) {
                    logger.warn("处理稿件 {} 时出现异常: {}", avid, e.getMessage());
                    log.warn("处理稿件 {} 时出现异常: {}", avid, e.getMessage());
                }
            }
            
            logger.info("DisableCommentTempusJob 执行成功, 总共处理: {} 个稿件", processedCount);
            log.info("DisableCommentTempusJob 执行成功, 总共处理: {} 个稿件", processedCount);
            return new ProcessResult(true, "success");
        } catch (Throwable t) {
            logger.error("DisableCommentTempusJob 执行失败", t);
            log.error("DisableCommentTempusJob 执行失败", t);
            return new ProcessResult(false, "执行失败: " + t.getMessage());
        }
    }
}
