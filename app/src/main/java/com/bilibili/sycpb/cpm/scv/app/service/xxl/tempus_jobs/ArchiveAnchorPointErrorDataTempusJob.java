package com.bilibili.sycpb.cpm.scv.app.service.xxl.tempus_jobs;

import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.tables.records.LauArchiveAnchorPointRecord;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.repo.LauArchiveAnchorPointRepo;
import com.bilibili.sycpb.cpm.scv.app.utils.enums.anchor.AuditTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;

/**
 * 锚点错误数据处理任务
 */
@Service
@Slf4j
public class ArchiveAnchorPointErrorDataTempusJob implements BasicProcessor {

    public static final String ID = "ArchiveAnchorPointErrorDataTempusJob";

    @Autowired
    private LauArchiveAnchorPointRepo lauArchiveAnchorPointRepo;

    @Override
    public ProcessResult process(TaskContext context) throws Exception {
        OmsLogger logger = context.getOmsLogger();
        String param = Optional.ofNullable(context.getJobParams()).orElse("");
        
        logger.info("ArchiveAnchorPointErrorDataTempusJob 开始执行, 参数: {}", param);
        log.info("ArchiveAnchorPointErrorDataTempusJob 开始执行, 参数: {}", param);
        
        try {
            Long id = 0L;
            List<LauArchiveAnchorPointRecord> records;
            int totalProcessed = 0;
            int batchCount = 0;

            do {
                records = lauArchiveAnchorPointRepo.queryAfterId(id, null, AuditTypeEnum.INIT.getCode(), 100);
                List<LauArchiveAnchorPointRecord> updateList = Lists.newArrayList();
                
                for (LauArchiveAnchorPointRecord record : records) {
                    // 触发 binlog 同步
                    LauArchiveAnchorPointRecord updateRecord = new LauArchiveAnchorPointRecord();
                    updateRecord.setMtime(new Timestamp(DateUtils.addSeconds(updateRecord.getMtime(), 1).getTime()));
                    updateRecord.setId(record.getId());
                    updateList.add(updateRecord);
                }

                if(!CollectionUtils.isEmpty(updateList)){
                    lauArchiveAnchorPointRepo.batchUpdate(updateList);
                    totalProcessed += updateList.size();
                    batchCount++;
                    logger.info("原生锚点异常数据同步中, 第 {} 批, size: {}", batchCount, updateList.size());
                    log.info("原生锚点异常数据同步中, 第 {} 批, size: {}", batchCount, updateList.size());
                }
                
                if (records != null && !records.isEmpty()) {
                    id = records.get(records.size() - 1).getId();
                }
            } while (null != records && records.size() == 100);
            
            logger.info("ArchiveAnchorPointErrorDataTempusJob 执行成功, 总共处理 {} 批, {} 条记录", batchCount, totalProcessed);
            log.info("ArchiveAnchorPointErrorDataTempusJob 执行成功, 总共处理 {} 批, {} 条记录", batchCount, totalProcessed);
            return new ProcessResult(true, "success");
        } catch (Exception e) {
            logger.error("ArchiveAnchorPointErrorDataTempusJob 执行失败", e);
            log.error("原生锚点异常数据同步失败", e);
            return new ProcessResult(false, "执行失败: " + e.getMessage());
        }
    }
}
