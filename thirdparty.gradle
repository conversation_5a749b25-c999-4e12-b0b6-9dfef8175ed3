ext {
    versions = [
            'pleiades'        : '1.2.8',
//            'warp'            : '1.1.7',
            'spring'          : '5.2.8.RELEASE',
            'caffeine'        : '2.6.2',
            'springboot'      : '2.3.3.RELEASE',
            'lombok'          : '1.18.24',
            'gradle-lombok'   : '5.0.0',
            'javax-annotation': '1.3.2',
            'redisson'        : '3.16.8',
            'bouncycastle'    : '1.46',
            'commons-lang'    : '3.12.0',
            'commons-io'      : '1.3.2',
            'commons-codec'   : '1.15',
            's3'              : '2.17.27',
            'jooq'            : '3.15.2',
            'bapi'            : '1.0.0-SNAPSHOT',
            'bapis-version': '1.29.0.1.master.17476606680000.2986586ff530',
            'jackson'         : '2.13.2',
            'jjwt'            : '0.11.2',
            'paladin-client'  : '2.0.6',
            'es'              : '6.7.2',
            'mapstruct'       : '1.5.3.Final',
//            'launch-biz'      : '8.1.45.62-SNAPSHOT',
    ]
    libs = [
            'caffeine-guava'              : "com.github.ben-manes.caffeine:caffeine:${versions['caffeine']}",
            'springboot-starter-web'      : "org.springframework.boot:spring-boot-starter-web:${versions['springboot']}",
            'springboot-starter-thymeleaf': "org.springframework.boot:spring-boot-starter-thymeleaf:${versions['springboot']}",
            'springboot-starter-aop'      : "org.springframework.boot:spring-boot-starter-aop:${versions['springboot']}",
            'spring-context-support'      : "org.springframework:spring-context-support:${versions['spring']}",
            'spring-jdbc'                 : "org.springframework:spring-jdbc:${versions['spring']}",
            'gradle-lombok'               : "io.franzbecker:gradle-lombok:${versions['gradle-lombok']}",
            'javax-annotation'            : "javax.annotation:javax.annotation-api:${versions['javax-annotation']}",
            'redisson'                    : "org.redisson:redisson:${versions['redisson']}",
            'bouncycastle'                : "org.bouncycastle:bcprov-jdk16:${versions['bouncycastle']}",
            'commons-lang'                : "org.apache.commons:commons-lang3:${versions['commons-lang']}",
            'commons-io'                  : "org.apache.commons:commons-io:${versions['commons-io']}",
            'commons-codec'               : "commons-codec:commons-codec:${versions['commons-codec']}",
            'jooq'                        : "org.jooq:jooq:${versions['jooq']}",
            'jooq-meta'                   : "org.jooq:jooq-meta:${versions['jooq']}",
            'jooq-codegen'                : "org.jooq:jooq-codegen:${versions['jooq']}",
            'lombok'                      : "org.projectlombok:lombok:${versions['lombok']}",
            'swagger'                     : "org.springdoc:springdoc-openapi-ui:1.6.14",
            'jackson-databind'            : "com.fasterxml.jackson.core:jackson-databind:${versions['jackson']}",
            'javers'                      : "org.javers:javers-core:6.6.0",
            'jjwt-api'                    : "io.jsonwebtoken:jjwt-api:${versions['jjwt']}",
            'jjwt-impl'                   : "io.jsonwebtoken:jjwt-impl:${versions['jjwt']}",
            'jjwt-jackson'                : "io.jsonwebtoken:jjwt-jackson:${versions['jjwt']}",
            'xxl'                         : "com.xuxueli:xxl-job-core:bili-2.5.4-RELEASE",
            'paladin-client'              : "com.bilibili:paladin-client:${versions['paladin-client']}",
            'paladin-okhttp-client'       : "com.bilibili:paladin-okhttp-client:${versions['paladin-client']}",
            'retrofit'                    : "com.squareup.retrofit2:retrofit:2.9.0",
            'retrofit-jackson'            : "com.squareup.retrofit2:converter-jackson:2.9.0",
            'javacv'                      : "org.bytedeco:javacv-platform:1.5.7",
            'es-rest-client'              : "org.elasticsearch.client:elasticsearch-rest-high-level-client:${versions['es']}",
            's3'                          : "com.amazonaws:aws-java-sdk-s3:1.12.272",
            'buf-gradle-plugin'           : "com.bilibili:buf-gradle-plugin:1.0.11",
            'mapstruct'                   : "org.mapstruct:mapstruct:${versions['mapstruct']}",
            'mapstruct-annotation'        : "org.mapstruct:mapstruct-processor:${versions['mapstruct']}",
            'lombok-mapstruct-binding'    : "org.projectlombok:lombok-mapstruct-binding:0.2.0",
            'mapstruct-protobuf-spi'      : "no.entur.mapstruct.spi:protobuf-spi-impl:1.44",
            'mas-api'                     : "com.bilibili.mas:mas-api:4.16.0-SNAPSHOT",
//            'ok2curl'                   : "io.github.dzsf:ok2curl:1.0.0",
            'easyexcel'                   : "com.alibaba:easyexcel:3.0.5",
            'poi'                         : "org.apache.poi:poi-ooxml-schemas:4.1.2",
            'bvutils'                     : "com.bilibili.bv:bvutils:1.0.0-RELEASE",
            'boggart-routing-ds'          : "sycpb.platform:boggart-routing-datasource:2.3-SNAPSHOT",
    ]

    component = [
            'http-server' : "pleiades.component.http:http-server:${versions['pleiades']}",
            'http-client' : "pleiades.component.http:http-client:${versions['pleiades']}",
            'rpc-server'  : "pleiades.component.rpc:rpc-server:${versions['pleiades']}",
            'rpc-client'  : "pleiades.component.rpc:rpc-client:${versions['pleiades']}",
            'memcache'    : "pleiades.component:memcache:${versions['pleiades']}",
            'tidb'        : "pleiades.component.datasource:tidb:${versions['pleiades']}",
            'mysql'       : "pleiades.component.datasource:mysql:${versions['pleiades']}",
            'utility'     : "pleiades.component:utility:${versions['pleiades']}",
            'test'        : "pleiades.component:test:${versions['pleiades']}",
            'hbase'       : "pleiades.component:hbase:${versions['pleiades']}",
            'databus'     : "pleiades.component:databus:${versions['pleiades']}",
            'databus-v2'  : "pleiades.component.databus:databus-v2:${versions['pleiades']}",
            'ecode'       : "pleiades.component:component-ecode:${versions['pleiades']}",
            'env'         : "pleiades.component:env:${versions['pleiades']}",
//            'warp-databus': "com.bilibili:warp-spring-boot-starter-databus:${versions['warp']}",
            'databus-java': "com.bilibili.microservices:databus-java:1.10.3-RELEASE"
    ]

    venus = [
            'starter'         : "pleiades.venus:starter:${versions['pleiades']}",
            'context'         : "pleiades.venus:context:${versions['pleiades']}",
            'logging'         : "pleiades.venus:logging:${versions['pleiades']}",
            'naming-discovery': "pleiades.venus.naming:naming-discovery:${versions['pleiades']}",
            'infoc'           : "pleiades.venus:infoc:${versions['pleiades']}",
    ]

    middleware = [
            'middleware-ecode': "pleiades.middleware:middleware-ecode:${versions['pleiades']}",
            'identify'        : "pleiades.middleware:identify:${versions['pleiades']}",
            'open'            : "pleiades.middleware:open:${versions['pleiades']}",
            'lock'            : "pleiades.middleware:lock:${versions['pleiades']}",
            'actionlog'       : "pleiades.middleware:actionlog:${versions['pleiades']}",
            'manager'         : "pleiades.middleware:manager:${versions['pleiades']}",
            'exp-http'        : "pleiades.middleware.exp:exp-http:${versions['pleiades']}",
            'exp-rpc'         : "pleiades.middleware.exp:exp-rpc:${versions['pleiades']}",
            'device-http'     : "pleiades.middleware.device:device-http:${versions['pleiades']}",
            'device-rpc'      : "pleiades.middleware.device:device-rpc:${versions['pleiades']}",
    ]

    proto = [
            'proto-utility': "pleiades.proto:proto-utility:${versions['pleiades']}"
    ]
}