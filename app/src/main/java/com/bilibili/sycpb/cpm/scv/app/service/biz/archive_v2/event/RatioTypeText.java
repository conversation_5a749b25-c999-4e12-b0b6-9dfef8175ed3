package com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.event;

import java.math.BigInteger;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/4/24
 */
public class RatioTypeText {


    /**
     * @param width
     * @param height
     * @return 统一输出 约分后的'{width}x{height}'
     */
    public static String toText(int width, int height) {

        int gcd = gcd(width, height);

        int widthMod = width / gcd;

        int heightMod = height / gcd;

        return String.format("%dx%d", widthMod, heightMod);

    }


    /**
     * 最大公约数
     *
     * @param a
     * @param b
     * @return
     */
    private static int gcd(Integer a, Integer b) {
        return BigInteger.valueOf(a).gcd(BigInteger.valueOf(b)).intValue();
    }

}
