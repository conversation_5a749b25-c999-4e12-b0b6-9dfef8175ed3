/*
 * Copyright (c) 2015-2021 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos;

import com.bapis.ad.mgk.material.MaterialReferenceSearchReq;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmArchiveListConditionV3Bo {

    private Integer archiveMode;


    /**
     * 个人视角的查询账户id，不需要素材去重
     */
    private List<Integer> accountIds;

    /**
     * 聚合视角（代理商视角）的皮来那个账户id， 需要素材去重
     */
    private List<Integer> accountIdsInAggregationView;

    private List<Long> mids;
    private List<String> videoMd5s;
    private Boolean isAuditPassed;
    private Boolean isShared;
    private Boolean isDeleted;
    private Integer ratioWidth;
    private Integer ratioHeight;
    private Integer minDuration;
    private Integer maxDuration;
    private Long minCtime;
    private Long maxCtime;
    private Integer pageNo;
    private Integer pageSize;
    private String name;
    private String title;

    // 中间结果&搜索条件
    private List<Long> avids;
    private List<String> uniqueIds;


    // 使用素材中心搜索能力
    private String materialSearchWord;

    private List<String> materialIds;

    private String materialName;

    private String orderBy;


    public boolean searchByMaterialIdCenter(){


        if (StringUtils.isNotEmpty(materialSearchWord)) {

            return true;
        }

        if (!CollectionUtils.isEmpty(materialIds)) {
            return true;
        }

        if (StringUtils.isNotEmpty(materialName)) {
            return true;
        }


        return false;
    }


    public MaterialReferenceSearchReq toMaterialReferenceSearchReq(int maxSize, List<Integer> accountIdWithShareMode) {
        return MaterialReferenceSearchReq.newBuilder()
            .setSearchWord(materialSearchWord)
            .addAllMaterialIds(Optional.ofNullable(materialIds).orElse(new ArrayList<>()))
            .setName(materialName)
            .addAllAccountIds(Optional.ofNullable(accountIdWithShareMode).orElse(new ArrayList<>()).stream().map(String::valueOf).collect(Collectors.toList()))
            .setPn(1)
            .setPs(maxSize)
            .build();
    }


}
