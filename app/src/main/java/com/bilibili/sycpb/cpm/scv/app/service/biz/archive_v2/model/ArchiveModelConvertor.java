package com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.model;

import com.bapis.ad.mgk.material.MaterialAggregationView;
import com.bapis.ad.mgk.material.MaterialAggregationViewPageReq;
import com.bapis.ad.mgk.material.MaterialAggregationViewPageReq.Builder;
import com.bapis.ad.mgk.material.MaterialIdRegisterReq;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.ArchivePipelineServerCodes;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.event.MaterialAuditStatus;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.event.MaterialVideo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.event.RatioTypeText;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.CmArchiveListConditionV3Bo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.MgkCmArchiveBo;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.generated.tables.records.MgkCmArchiveRecord;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import io.vavr.control.Try;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/4/28
 */
@Mapper
public interface ArchiveModelConvertor {

    ArchiveModelConvertor instance = Mappers.getMapper(ArchiveModelConvertor.class);

    DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    String VIDE_REFERENCE_UK_PREFIX = "scv-archive-";

    String registerSource = "sycpb.cpm.scv";

    //    @Mapping(target = "accountId", source = "record.accountId")
//    @Mapping(target = "materialId", source = "materialId")
    @Mapping(target = "materialType", constant = "video")
    @Mapping(target = "materialUk", source = "record.videoMd5")
    @Mapping(target = "materialName", source = "record.title")
//    @Mapping(target = "ctime", source = "record.ctime")
//    @Mapping(target = "mtime", source = "record.mtime")
    @Mapping(target = "auditStatus", expression = "java(toMaterialAuditStatus(record.getAuditStatus()))")
    @Mapping(target = "strAttr1", source = "record.auditReason")
//    @Mapping(target = "width", source = "record.width")
//    @Mapping(target = "height", source = "record.height")
    @Mapping(target = "ratioTypeText", expression = "java(toRatioTypeText(record.getWidth(), record.getHeight()))")
    @Mapping(target = "duration", expression = "java(record.getDurationInMs()/ 1000)")
//    @Mapping(target = "avid", source = "record.avid")
//    @Mapping(target = "cid", source = "record.cid")
//    @Mapping(target = "coverUrl", source = "record.coverUrl")
//    @Mapping(target = "coverMd5", source = "record.coverMd5")
    @Mapping(target = "isDeleted", expression = "java(record.getIsDeleted() == 1)")
    MaterialVideo record2MaterialVideoWithNoShareTargetId(MgkCmArchiveRecord record, String materialId);


    default String toMaterialAuditStatus(Integer auditStatus) {

        if (auditStatus >= 0) {
            return MaterialAuditStatus.pass.name();
        } else if (ArchivePipelineServerCodes.fetchFailedAuditStatusCode().contains(auditStatus)) {
            return MaterialAuditStatus.reject.name();
        } else {
            return MaterialAuditStatus.auditing.name();
        }

    }

    default Integer toAuditStatusCode(String materialAuditStatus) {

        if (MaterialAuditStatus.pass.name().equals(materialAuditStatus)) {
            return 0;
        } else if (MaterialAuditStatus.reject.name().equals(materialAuditStatus)) {
            return ArchivePipelineServerCodes.FAILED.getCode();
        } else if (MaterialAuditStatus.auditing.name().equals(materialAuditStatus)) {
            return ArchivePipelineServerCodes.PROCESSING.getCode();
        } else {
            return -1;
        }
    }

    default String toRatioTypeText(int width, int height) {
        return Try.of(() -> RatioTypeText.toText(width, height)).getOrElse("");
    }


    default MaterialAggregationViewPageReq toAggregationViewPageReq(CmArchiveListConditionV3Bo condition) {

        Builder query = MaterialAggregationViewPageReq.newBuilder();

        query.addAllAccountIds(condition.getAccountIdsInAggregationView());

        query.setKeyword(Optional.ofNullable(condition.getMaterialSearchWord()).orElse(""));
        if (condition.getIsAuditPassed() != null) {
            query.addAllAuditStatus(condition.getIsAuditPassed() ?
                    Lists.newArrayList(MaterialAuditStatus.pass.name()) :
                    Lists.newArrayList(MaterialAuditStatus.reject.name(), MaterialAuditStatus.auditing.name()));

        }

        if (condition.getRatioHeight() != null && condition.getRatioWidth() != null
                && condition.getRatioHeight() > 0 && condition.getRatioWidth() > 0) {
            query.addRatioTypeText(RatioTypeText.toText(condition.getRatioWidth(), condition.getRatioHeight()));

        }

        if (condition.getMinDuration() != null) {
            query.setDurationGte(condition.getMinDuration() / 1000);
        }

        if (condition.getMaxDuration() != null) {
            query.setDurationLte(condition.getMaxDuration() / 1000);
        }

        return query.setPn(condition.getPageNo())
                .setPs(condition.getPageSize())
                .build();


    }

    //    @Mapping(target = "materialId", source = "materialId")
    @Mapping(target = "videoMd5", source = "materialUk")
    @Mapping(target = "title", source = "materialName")
    @Mapping(target = "ctime", expression = "java(dateTime2timestamp(grpc.getLastCtime()))")
    @Mapping(target = "mtime", expression = "java(dateTime2timestamp(grpc.getLastMtime()))")
    @Mapping(target = "auditStatus", expression = "java(toAuditStatusCode(grpc.getAuditStatus()))")
//    @Mapping(target = "width", source = "width")
//    @Mapping(target = "height", source = "height")
    @Mapping(target = "durationInMs", expression = "java(grpc.getDuration() * 1000)")
//    @Mapping(target = "avid", source = "avid")
//    @Mapping(target = "cid", source = "cid")
//    @Mapping(target = "coverUrl", source = "coverUrl")
//    @Mapping(target = "coverMd5", source = "coverMd5")
    @Mapping(target = "isDeleted", expression = "java(grpc.getIsDeleted() ? 1 : 0)")
    @Mapping(target = "sizeKb", constant = "0")
    @Mapping(target = "sizeType", constant = "0")
    @Mapping(target = "mid", constant = "0L")
    @Mapping(target = "catOne", constant = "0")
    @Mapping(target = "catTwo", constant = "0")
    @Mapping(target = "tags", constant = "")
    @Mapping(target = "auditReason", source = "strAttr1", defaultValue = "")
    @Mapping(target = "auditPassTime", expression = "java(dateTime2timestamp(grpc.getLastCtime()))")
    MgkCmArchiveBo aggregationViewToArchiveRecord(MaterialAggregationView grpc);


    default Timestamp dateTime2timestamp(String date) {
        return new Timestamp(
                LocalDateTime.parse(date, df).atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli());
    }


    default MaterialIdRegisterReq record2MaterialRegisterReq(MgkCmArchiveRecord record, String fileName) {

        return MaterialIdRegisterReq.newBuilder()
                .setMaterialUk(record.getVideoMd5())
                .setMaterialIdType("video")
                .setReferenceUk(fetchReferenceUk(record))
                .setName(this.asMaterialName(record, fileName))
                .setAccountId(String.valueOf(record.getAccountId()))
                .setContent(String.valueOf(record.getAvid()))
                .setSource(registerSource)
                .setForceUpdate(true)
                .build();
    }

    default String asMaterialName(MgkCmArchiveRecord record, String filename) {
        return Joiner.on("_").join(record.getTitle(), Optional.ofNullable(filename).orElse(""));
    }


    default String fetchReferenceUk(MgkCmArchiveRecord record) {

        return VIDE_REFERENCE_UK_PREFIX +
                Joiner.on("_").join(
                        record.getUniqueId(),
                        Optional.ofNullable(record.getId()).orElse(0));

    }

    default Optional<String> extractPrimaryIdFromReferenceUk(String referenceUk) {

        if (StringUtils.isEmpty(referenceUk)) {

            return Optional.empty();
        }
        if (referenceUk.startsWith(VIDE_REFERENCE_UK_PREFIX)) {
            return Optional.ofNullable(Try.of(() -> {
                String uniqueIdAndPk = referenceUk.substring(VIDE_REFERENCE_UK_PREFIX.length());

                return Splitter.on("_").splitToList(uniqueIdAndPk).get(0);

            }).getOrNull());

        } else {
            return Optional.empty();
        }

    }


    default Optional<Integer> extractPrimaryKeyFromReferenceUk(String referenceUk) {

        if (StringUtils.isEmpty(referenceUk)) {

            return Optional.empty();
        }
        if (referenceUk.startsWith(VIDE_REFERENCE_UK_PREFIX)) {
            return Optional.ofNullable(Try.of(() -> {
                String uniqueIdAndPk = referenceUk.substring(VIDE_REFERENCE_UK_PREFIX.length());

                return Integer.valueOf(Splitter.on("_").splitToList(uniqueIdAndPk).get(1));

            }).getOrNull());

        } else {
            return Optional.empty();
        }

    }

}
