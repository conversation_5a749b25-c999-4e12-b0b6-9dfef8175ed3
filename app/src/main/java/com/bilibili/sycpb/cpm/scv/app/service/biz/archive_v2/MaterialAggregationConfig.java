package com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/4/27
 */
@Data
@Configuration
public class MaterialAggregationConfig {


    @Value("${material.aggregation.pub.lock-key:material.v.agg.lock.pub.%s}")
    private String materialAggregationLockProduceKey;


    @Value("${material.aggregation.sub.lock-key:material.v.agg.lock.sub.%s}")
    private String materialAggregationLockConsumeKey;


    /**
     * 可以很小，同时执行就跳过当前也行
     */
    @Value("${material.aggregation.lock.wait-millis:100}")
    private Long materialAggregationLockWaitMillis;

    @Value("${material.aggregation.lock.lease-millis:500}")
    private Long materialAggregationLockLeaseMillis;
}
