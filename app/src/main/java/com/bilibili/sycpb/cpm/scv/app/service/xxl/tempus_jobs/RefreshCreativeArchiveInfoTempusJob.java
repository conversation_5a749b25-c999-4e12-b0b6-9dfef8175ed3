package com.bilibili.sycpb.cpm.scv.app.service.xxl.tempus_jobs;

import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.AdDataSourceConfig;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.LaunchArchiveService;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import java.sql.Timestamp;
import java.time.Instant;
import java.util.Optional;

/**
 * 刷新创意稿件信息任务
 * <AUTHOR>
 */
@Slf4j
@Service
public class RefreshCreativeArchiveInfoTempusJob implements BasicProcessor {
    
    public static final String ID = "RefreshCreativeArchiveInfoTempusJob";
    private final LaunchArchiveService launchArchiveService;

    public RefreshCreativeArchiveInfoTempusJob(@Qualifier(AdDataSourceConfig.AD_DSL_CONTEXT) DSLContext ad, 
                                               LaunchArchiveService launchArchiveService) {
        this.launchArchiveService = launchArchiveService;
    }

    @Override
    public ProcessResult process(TaskContext context) throws Exception {
        OmsLogger logger = context.getOmsLogger();
        String jobParams = Optional.ofNullable(context.getJobParams()).orElse("");
        
        logger.info("RefreshCreativeArchiveInfoTempusJob 开始执行, 参数: {}", jobParams);
        log.info("RefreshCreativeArchiveInfoTempusJob 开始执行, 参数: {}", jobParams);
        
        try {
            // 每次处理5分钟前的数据
            final var now = Instant.now();
            final var startTime = Timestamp.from(now.minusSeconds(5*60));
            final var endTime = Timestamp.from(now);
            
            logger.info("处理时间范围: {} 到 {}", startTime, endTime);
            log.info("处理时间范围: {} 到 {}", startTime, endTime);
            
            launchArchiveService.updateArcs(startTime, endTime);
            
            logger.info("RefreshCreativeArchiveInfoTempusJob 执行成功");
            log.info("RefreshCreativeArchiveInfoTempusJob 执行成功");
            return new ProcessResult(true, "success");
        } catch (Throwable t) {
            logger.error("RefreshCreativeArchiveInfoTempusJob 执行失败", t);
            log.error("RefreshCreativeArchiveInfoTempusJob 执行失败", t);
            return new ProcessResult(false, "执行失败: " + t.getMessage());
        }
    }
}
