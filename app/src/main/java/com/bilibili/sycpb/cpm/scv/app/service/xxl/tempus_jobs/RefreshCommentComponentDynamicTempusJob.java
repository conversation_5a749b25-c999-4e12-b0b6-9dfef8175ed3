package com.bilibili.sycpb.cpm.scv.app.service.xxl.tempus_jobs;

import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.AdDataSourceConfig;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.tables.records.LauArchiveCommentConversionComponentRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.Result;
import org.jooq.impl.DSL;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import javax.annotation.Resource;
import java.util.Optional;

import static com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.tables.TLauArchiveCommentConversionComponent.LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT;

/**
 * 刷新评论组件动态任务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RefreshCommentComponentDynamicTempusJob implements BasicProcessor {

    public static final String ID = "RefreshCommentComponentDynamicTempusJob";

    @Resource(name = AdDataSourceConfig.AD_DSL_CONTEXT)
    private DSLContext ad;

    @Autowired
    private RedissonClient redissonClient;

    private static final String DYNAMIC_PREFIX = "scv.dynamic.component-";

    @Override
    public ProcessResult process(TaskContext context) throws Exception {
        OmsLogger logger = context.getOmsLogger();
        String jobParams = Optional.ofNullable(context.getJobParams()).orElse("");
        
        logger.info("RefreshCommentComponentDynamicTempusJob 开始执行, 参数: {}", jobParams);
        log.info("RefreshCommentComponentDynamicTempusJob 开始执行, 参数: {}", jobParams);
        
        try {
            Long index = 0L;
            Long maxId = ad.select(DSL.max(LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.ID))
                          .from(LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT)
                          .fetchOne().value1();
                          
            logger.info("开始刷新评论组件动态, 最大ID: {}", maxId);
            log.info("开始刷新评论组件动态, 最大ID: {}", maxId);
            
            int totalProcessed = 0;
            int batchCount = 0;

            while (true) {
                if (index * 100 > maxId) {
                    break;
                }

                Result<LauArchiveCommentConversionComponentRecord> componentRecords = ad.selectFrom(LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT)
                        .where(LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.ID.greaterOrEqual(index * 100)
                                .and(LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.ID.lessThan((index + 1) * 100))
                                .and(LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.DYNAMIC_ID.notEqual(0L)))
                        .fetch();

                if (componentRecords.isEmpty()) {
                    index++;
                    continue;
                }
                
                for (LauArchiveCommentConversionComponentRecord componentRecord : componentRecords) {
                    Long dynamicId = componentRecord.getDynamicId();
                    RBucket<Long> dynamicBucket = redissonClient.getBucket(DYNAMIC_PREFIX + dynamicId);
                    dynamicBucket.set(componentRecord.getCommentId());
                    totalProcessed++;
                }
                
                index++;
                batchCount++;
                
                logger.info("完成第 {} 批处理, 当前索引: {}, 本批处理: {} 条", batchCount, index, componentRecords.size());
                if (batchCount % 10 == 0) {
                    log.info("完成第 {} 批处理, 当前索引: {}, 累计处理: {} 条", batchCount, index, totalProcessed);
                }
                
                Thread.sleep(150);
            }
            
            logger.info("RefreshCommentComponentDynamicTempusJob 执行成功, 总共处理 {} 批, {} 条记录", batchCount, totalProcessed);
            log.info("RefreshCommentComponentDynamicTempusJob 执行成功, 总共处理 {} 批, {} 条记录", batchCount, totalProcessed);
            return new ProcessResult(true, "success");
        } catch (Throwable t) {
            logger.error("RefreshCommentComponentDynamicTempusJob 执行失败", t);
            log.error("RefreshCommentComponentDynamicTempusJob 执行失败", t);
            return new ProcessResult(false, "执行失败: " + t.getMessage());
        }
    }
}
