package com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.event;


import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.ApplicationEvent;

/**
 * TODO 这个事件处理的顺序性非常关键，防止ABA问题。
 * <p>
 * TODO 考虑下是否需要全程加锁， 特别是回查所有素材关联引用的过程是否需要加锁。 目前看可能不行，因为存在三方的素材。scv的稿件我们可以认为三方素材
 * <p>
 * 最终方案，发送端加锁 + 队列partition 有序+ 消费端加锁 最终保证有序；
 *
 * <AUTHOR>
 * @desc
 * @date 2025/4/24
 */
@Setter
@Getter
@Accessors(chain = true)
public class ArchiveAggregationUpdateEvent extends ApplicationEvent {

    private String materialId;

    private String materialType = "video";

    private String materialUk;

    private List<MaterialVideo> archives;

    public ArchiveAggregationUpdateEvent(Object source) {
        super(source);
    }
}
