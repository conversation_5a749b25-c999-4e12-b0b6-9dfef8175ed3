package com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos;


import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/9/13
 * @description record转bo
 */
@Data
@Accessors(chain = true)
public class MgkCmArchiveBo implements Serializable {



    /**
     * Getter for <code>mgk_cm_archive.id</code>. id
     */
    public Integer id;
    /**
     * Getter for <code>mgk_cm_archive.is_deleted</code>. 是否删除0否 1是
     */
    public Integer isDeleted;
    /**
     * Getter for <code>mgk_cm_archive.ctime</code>. 创建时间
     */
    public Timestamp ctime;
    /**
     * Getter for <code>mgk_cm_archive.mtime</code>. 修改时间
     */
    public Timestamp mtime;
    /**
     * Getter for <code>mgk_cm_archive.biz_id</code>. (已废弃)ssa视频物料id
     */
    public Integer bizId;
    /**
     * Getter for <code>mgk_cm_archive.mid</code>. 投稿使用的主站mid
     */
    public Long mid;
    /**
     * Getter for <code>mgk_cm_archive.avid</code>. 投稿得到的主站稿件id
     */
    public Long avid;
    /**
     * Getter for <code>mgk_cm_archive.cid</code>. ugc视频物料id
     */
    public Long cid;
    public String ugcFilename;
    /**
     * Getter for <code>mgk_cm_archive.cover_url</code>. 稿件封面url
     */
    public String coverUrl;
    /**
     * Getter for <code>mgk_cm_archive.cover_md5</code>. 稿件封面md5
     */
    public String coverMd5;
    /**
     * Getter for <code>mgk_cm_archive.width</code>. 视频物料宽
     */
    public Integer width;
    /**
     * Getter for <code>mgk_cm_archive.height</code>. 视频物料高
     */
    public Integer height;
    /**
     * Getter for <code>mgk_cm_archive.duration_in_ms</code>. 视频物料时长(毫秒)
     */
    public Integer durationInMs;
    /**
     * Getter for <code>mgk_cm_archive.size_kb</code>. 视频大小(KB)
     */
    public Integer sizeKb;
    /**
     * Getter for <code>mgk_cm_archive.size_type</code>.
     * (已废弃)视频云转码后得到的视频宽高比分类：0-其他 1-16:9 2-9:16 3-3:4 4-4:3
     */
    public Integer sizeType;
    /**
     * Getter for <code>mgk_cm_archive.audit_status</code>. 主站审核状态: 大于等于0代表审核通过
     */
    public Integer auditStatus;
    /**
     * Getter for <code>mgk_cm_archive.audit_reason</code>. 主站审核拒绝原因
     */
    public String auditReason;
    /**
     * Getter for <code>mgk_cm_archive.audit_pass_time</code>. 主站审核通过时间
     */
    public Timestamp auditPassTime;
    /**
     * Getter for <code>mgk_cm_archive.ratio_width</code>. 视频宽高比(宽)
     */
    public Integer ratioWidth;
    /**
     * Getter for <code>mgk_cm_archive.ratio_height</code>. 视频宽高比(高)
     */
    public Integer ratioHeight;
    /**
     * Getter for <code>mgk_cm_archive.ssa_filename</code>.
     * (已废弃)用于ssa投稿的filename
     */
    public String ssaFilename;
    /**
     * Getter for <code>mgk_cm_archive.video_md5</code>. 原始文件md5值, 用来关联视频表
     */
    public String videoMd5;
    /**
     * Getter for <code>mgk_cm_archive.account_id</code>. 账号id
     */
    public Integer accountId;
    /**
     * Getter for <code>mgk_cm_archive.archive_mode</code>. 投稿模式: 0-子账号投稿,
     * 1-用户空间投稿, 2-品牌ogv
     */
    public Integer archiveMode;
    /**
     * Getter for <code>mgk_cm_archive.public_encoded_url</code>. 公网授权视频链接
     */
    public String publicEncodedUrl;
    /**
     * Getter for <code>mgk_cm_archive.xcode_status</code>. (已废弃)转码状态: 0-转码中;
     * 1-转码失败; 2-转码成功
     */
    public Integer xcodeStatus;
    /**
     * Getter for <code>mgk_cm_archive.auth_status</code>. (已废弃)授权状态: 0-等待授权;
     * 1-授权成功; 2-授权失败
     */
    public Integer authStatus;
    /**
     * Getter for <code>mgk_cm_archive.title</code>. 稿件标题
     */
    public String title;
    /**
     * Getter for <code>mgk_cm_archive.tags</code>. 稿件标签
     */
    public String tags;
    /**
     * Getter for <code>mgk_cm_archive.cat_one</code>. 一级分区
     */
    public Integer catOne;
    /**
     * Getter for <code>mgk_cm_archive.cat_two</code>. 二级分区
     */
    public Integer catTwo;
    /**
     * Getter for <code>mgk_cm_archive.auth_time</code>. 授权时间
     */
    public Timestamp authTime;
    /**
     * Getter for <code>mgk_cm_archive.ipv6</code>. 用户投稿时的活跃ip
     */
    public byte[] ipv6;
    /**
     * Getter for <code>mgk_cm_archive.source</code>. mid池的key(mgk_adx_space)
     */
    public Integer source;
    /**
     * Getter for <code>mgk_cm_archive.register_dm</code>. 注册过弹幕
     */
    public Integer registerDm;
    /**
     * Getter for <code>mgk_cm_archive.pipeline_status</code>. 0-初始化, 1-进行中,
     * 2-执行成功, 3-执行失败, 4-无需执行
     */
    public Integer pipelineStatus;
    /**
     * Getter for <code>mgk_cm_archive.pipeline_stage</code>.
     */
    public Integer pipelineStage;
    /**
     * Getter for <code>mgk_cm_archive.unique_id</code>. 唯一id
     */
    public String uniqueId;
    /**
     * Getter for <code>mgk_cm_archive.video_url</code>. 视频url
     */
    public String videoUrl;
    /**
     * Getter for <code>mgk_cm_archive.cover_origin</code>.
     * 稿件封面类型，1-opencv或智能裁剪，2-自定义，0-其他
     */
    public Integer coverOrigin;

    /**
     * 稿件来源，本地上传、推送
     */
    public Integer archiveSource;


    /**
     *
     */
    private String materialId;

    /**
     * 素材持有者accountId
     */
    private List<Integer> accountIds = new ArrayList<>();

    /**
     * 推送的来源账户
     */
    private List<Integer> pushSourceIds = new ArrayList<>();

    /**
     * 分享的来源账户
     */
    private List<Integer> shareSourceIds = new ArrayList<>();


    /**
     * 推送的目标账户, 需要注意已经丢失了关联关系
     */
    private List<Integer> pushTargetIds = new ArrayList<>();
    /**
     * 分享的目标账户, 需要注意已经丢失了关联关系
     */

    private List<Integer> shareTargetIds = new ArrayList<>();


}
