/*
 * Copyright (c) 2015-2022 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.service.xxl.tempus_jobs;

import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.MgkDataSourceConfig;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.generated.Tables.MGK_CM_ARCHIVE;

/**
 * CID过期处理任务
 * 主站视频转码48小时之内如果不投稿就无法使用了
 * 这种情况我们丢弃这个数据是合理的
 */
@Slf4j
@Service
public class CidExpirationTempusJob implements BasicProcessor {
    
    public static final String ID = "CidExpirationTempusJob";
    private final DSLContext mgk;

    public CidExpirationTempusJob(@Qualifier(MgkDataSourceConfig.MGK_DSL_CONTEXT) DSLContext mgk) {
        this.mgk = mgk;
    }

    @Override
    public ProcessResult process(TaskContext context) throws Exception {
        OmsLogger logger = context.getOmsLogger();
        String jobParams = Optional.ofNullable(context.getJobParams()).orElse("");
        
        logger.info("CidExpirationTempusJob 开始执行, 参数: {}", jobParams);
        log.info("CidExpirationTempusJob 开始执行, 参数: {}", jobParams);
        
        try {
            final Timestamp expirationTs = java.sql.Timestamp.valueOf(LocalDateTime.now().minusHours(48));
            logger.info("查询过期时间点: {}", expirationTs);
            
            final List<Integer> ids = mgk.select(MGK_CM_ARCHIVE.ID)
                .from(MGK_CM_ARCHIVE)
                .where(MGK_CM_ARCHIVE.AVID.eq(0L)
                    .and(MGK_CM_ARCHIVE.CTIME.lt(expirationTs)))
                .fetch(MGK_CM_ARCHIVE.ID);
                
            logger.info("找到需要删除的过期CID数量: {}", ids.size());
            log.info("找到需要删除的过期CID数量: {}", ids.size());
            
            if (!CollectionUtils.isEmpty(ids)) {
                int deletedCount = mgk.delete(MGK_CM_ARCHIVE)
                    .where(MGK_CM_ARCHIVE.ID.in(ids))
                    .execute();
                    
                logger.info("成功删除过期CID数据: {} 条", deletedCount);
                log.info("成功删除过期CID数据: {} 条", deletedCount);
            } else {
                logger.info("没有找到需要删除的过期CID数据");
                log.info("没有找到需要删除的过期CID数据");
            }
            
            logger.info("CidExpirationTempusJob 执行成功");
            log.info("CidExpirationTempusJob 执行成功");
            return new ProcessResult(true, "success");
        } catch (Throwable t) {
            logger.error("CidExpirationTempusJob 执行失败", t);
            log.error("CidExpirationTempusJob 执行失败", t);
            return new ProcessResult(false, "执行失败: " + t.getMessage());
        }
    }
}
