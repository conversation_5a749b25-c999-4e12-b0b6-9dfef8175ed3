package com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/5/15
 */
@Data
@Accessors(chain = true)
public class MaterialAggregationInfoBo {


    /**
     * 素材持有者accountId
     */
    private List<Integer> accountIds = new ArrayList<>();

    /**
     * 推送的来源账户
     */
    private List<Integer> pushSourceIds = new ArrayList<>();

    /**
     * 分享的来源账户
     */
    private List<Integer> shareSourceIds = new ArrayList<>();


    /**
     * 推送的目标账户, 需要注意已经丢失了关联关系
     */
    private List<Integer> pushTargetIds = new ArrayList<>();
    /**
     * 分享的目标账户, 需要注意已经丢失了关联关系
     */

    private List<Integer> shareTargetIds = new ArrayList<>();


}
