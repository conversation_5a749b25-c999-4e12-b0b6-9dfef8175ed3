package com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2;


import io.vavr.CheckedFunction0;
import io.vavr.control.Try;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/4/25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialAggregationLock {

    private final RedissonClient redissonClient;


    private final MaterialAggregationConfig materialAggregationConfig;


    public <T> T lockProduceProcedure(String materialId, CheckedFunction0<T> func) throws Throwable {

        return handleWithLock(
                String.format(materialAggregationConfig.getMaterialAggregationLockProduceKey(), materialId),
                func
        );

    }


    public <T> T lockConsumeProcedure(String materialId, CheckedFunction0<T> func) throws Throwable {

        return handleWithLock(
                String.format(materialAggregationConfig.getMaterialAggregationLockConsumeKey(), materialId),
                func
        );

    }


    private <T> T handleWithLock(String key, CheckedFunction0<T> func) throws Throwable {

        RLock lock = redissonClient.getLock(key);

        if (!tryLock(lock)) {
            log.warn("Fail to acquire material-aggregation lock for material={}", key);
            throw new RuntimeException("获取素材聚合执行锁失败,可能有同一素材任务已经在执行");
        }
        try {
            return func.apply();
        } finally {
            releaseLock(lock);
        }

    }


    private boolean tryLock(RLock lock) {

        try {
            return lock.tryLock(
                    materialAggregationConfig.getMaterialAggregationLockWaitMillis(),
                    materialAggregationConfig.getMaterialAggregationLockLeaseMillis(),
                    TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {

            throw new RuntimeException("获取锁失败, interrupted", e);
        }
    }


    private void releaseLock(RLock lock) {
        Try.run(lock::unlock).onFailure(t -> {
            log.warn("Fail to unlock crop lock", t);
        });
    }


}
